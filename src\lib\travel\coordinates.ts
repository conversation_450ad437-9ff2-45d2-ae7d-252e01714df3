/**
 * Coordinates Module - Google Places API Integration
 * OTIMIZADO para redução de custos com cache inteligente
 */

import type { PlacePhoto, PhotoRequest } from '@/types/travel';

// Types for OpenStreetMaps/OpenRouteService compatibility
export interface Coordinates {
  latitude: number;
  longitude: number;
  address: string;
  // New photo fields
  photo_url?: string;
  photo_attribution?: string;
  photos?: PlacePhoto[];
}

// Enhanced rate limiting with intelligent backoff
const API_CONFIG = {
  baseDelay: 100, // Start with 100ms
  maxDelay: 5000, // Max 5 seconds
  retryAttempts: 3,
  batchSize: 5 // Process max 5 at once
};

// Queue para evitar rate limiting
const requestQueue: Array<() => Promise<any>> = [];
let isProcessingQueue = false;

// API request with intelligent rate limiting
async function makeGooglePlacesRequest(
  query: string,
  type: 'textSearch' | 'placeDetails' = 'textSearch',
  placeId?: string,
  includePhotos: boolean = true
): Promise<any> {
  return new Promise((resolve, reject) => {
    const request = async () => {
      try {
        const API_KEY = process.env['GOOGLE_PLACES_API_KEY'];
        if (!API_KEY) {
          throw new Error('Google Places API key not configured');
        }

        let url: string;
        
        if (type === 'textSearch') {
          // Optimize text search with minimal fields
          const fields = includePhotos 
            ? 'place_id,name,geometry,formatted_address,photos'
            : 'place_id,name,geometry,formatted_address';
            
          url = `https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(query)}&fields=${fields}&key=${API_KEY}&language=pt-BR`;
        } else {
          // Optimize place details with minimal fields
          const fields = includePhotos
            ? 'place_id,name,geometry,formatted_address,photos'
            : 'place_id,name,geometry,formatted_address';
            
          url = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&fields=${fields}&key=${API_KEY}&language=pt-BR`;
        }

        console.log(`🔍 Optimized Google Places request: ${type}`);
        
        const response = await fetch(url, {
          headers: {
            'User-Agent': 'Voyagr/1.0 (Travel Planner)'
          }
        });

        if (!response.ok) {
          throw new Error(`Google Places API error: ${response.status}`);
        }

        const data = await response.json();
        
        if (data.status === 'OVER_QUERY_LIMIT' || data.status === 'REQUEST_DENIED') {
          throw new Error(`Google Places API: ${data.status} - ${data.error_message || 'Rate limit or quota exceeded'}`);
        }

        resolve(data);
      } catch (error) {
        reject(error);
      }
    };

    // Add to queue
    requestQueue.push(request);
    processQueue();
  });
}

// Intelligent queue processing with rate limiting
async function processQueue(): Promise<void> {
  if (isProcessingQueue || requestQueue.length === 0) {
    return;
  }

  isProcessingQueue = true;

  while (requestQueue.length > 0) {
    const batch = requestQueue.splice(0, API_CONFIG.batchSize);
    
    // Process batch with controlled timing
    const promises = batch.map(async (request, index) => {
      // Stagger requests to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, index * 200));
      return request();
    });

    try {
      await Promise.all(promises);
      // Wait between batches
      if (requestQueue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error) {
      console.warn('Batch processing error:', error);
      // Continue processing remaining requests
    }
  }

  isProcessingQueue = false;
}

export async function getLocationCoordinates(
  locationName: string,
  locationType: string,
  city: string,
  includePhotos: boolean = true
): Promise<Coordinates | null> {
  try {
    console.log(`🎯 Fetching coordinates: ${locationName} (${locationType}) in ${city}`);
    
    // Construct optimized search query
    const searchQuery = `${locationName}, ${city}, Brasil`;
    
    // Make optimized API request
    const data = await makeGooglePlacesRequest(searchQuery, 'textSearch', undefined, includePhotos);
    
    if (!data.results || data.results.length === 0) {
      console.log(`❌ No results found for: ${locationName}`);
      return null;
    }

    const result = data.results[0];
    const coordinates: Coordinates = {
      latitude: result.geometry.location.lat,
      longitude: result.geometry.location.lng,
      address: result.formatted_address || `${locationName}, ${city}, Brasil`
    };

    // Add photo with optimization
    if (includePhotos && result.photos && result.photos.length > 0) {
      const photo = result.photos[0];
      const photoUrl = constructPhotoUrl(photo.photo_reference, 800, 600);
      coordinates.photo_url = photoUrl;
    }

    console.log(`✅ Coordinates found: [${coordinates.latitude}, ${coordinates.longitude}]`);
    return coordinates;

  } catch (error) {
    console.error(`❌ Error fetching coordinates for ${locationName}:`, error);
    return null;
  }
}

function constructPhotoUrl(photoReference: string, maxWidth: number = 800, maxHeight: number = 600): string {
  const API_KEY = process.env['GOOGLE_PLACES_API_KEY'];
  return `https://maps.googleapis.com/maps/api/place/photo?maxwidth=${maxWidth}&maxheight=${maxHeight}&photo_reference=${photoReference}&key=${API_KEY}`;
}

// Function declaration for Gemini AI with photos
export const getLocationCoordinatesDeclaration = {
  name: "get_location_coordinates",
  description: "Get exact latitude, longitude coordinates and photos for a specific location using Google Places API. Essential for map integration with OpenStreetMaps and rich visual content.",
  parameters: {
    type: "object",
    properties: {
      location_name: {
        type: "string",
        description: "Complete name of the location, attraction, restaurant, or place. Include city and state/country for better accuracy."
      },
      location_type: {
        type: "string",
        enum: ["restaurant", "attraction", "hotel", "shopping", "transportation", "entertainment", "museum", "park", "beach", "landmark"],
        description: "Type of location to help with accurate search"
      },
      city: {
        type: "string", 
        description: "City where the location is situated"
      },
      include_photos: {
        type: "boolean",
        description: "Whether to include photos from Google Places. Default: true"
      }
    },
    required: ["location_name", "location_type", "city"]
  }
};

// Generate Google Places Photo URL
export function generatePhotoUrl(
  photoName: string, 
  maxWidth: number = 800, 
  maxHeight: number = 600
): string {
  const GOOGLE_PLACES_API_KEY = process.env['GOOGLE_PLACES_API_KEY'];
  
  if (!GOOGLE_PLACES_API_KEY) {
    return `https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=${maxWidth}&h=${maxHeight}&fit=crop&crop=center`;
  }

  const params = new URLSearchParams({
    key: GOOGLE_PLACES_API_KEY,
    maxWidthPx: maxWidth.toString(),
    maxHeightPx: maxHeight.toString()
  });

  return `https://places.googleapis.com/v1/${photoName}/media?${params.toString()}`;
}

// Fetch photo with custom dimensions
export async function getPlacePhoto(request: PhotoRequest): Promise<string | null> {
  try {
    const url = generatePhotoUrl(
      request.name, 
      request.maxWidthPx || 800, 
      request.maxHeightPx || 600
    );

    // Return the URL directly - skip HTTP redirect check
    return url;
  } catch (error) {
    console.error('Error fetching place photo:', error);
    return null;
  }
}

// Mock coordinates with photos for development (removed unused function)

export function getMockCoordinatesForCity(city: string): { lat: number; lng: number } {
  const cityCoords: Record<string, { lat: number; lng: number }> = {
    'são paulo': { lat: -23.5505, lng: -46.6333 },
    'rio de janeiro': { lat: -22.9068, lng: -43.1729 },
    'salvador': { lat: -12.9714, lng: -38.5014 },
    'brasília': { lat: -15.7801, lng: -47.9292 },
    'fortaleza': { lat: -3.7172, lng: -38.5433 },
    'belo horizonte': { lat: -19.9167, lng: -43.9345 },
    'manaus': { lat: -3.1190, lng: -60.0217 },
    'curitiba': { lat: -25.4244, lng: -49.2654 },
    'recife': { lat: -8.0476, lng: -34.8770 },
    'porto alegre': { lat: -30.0346, lng: -51.2177 }
  };

  const normalizedCity = city.toLowerCase().trim();
  return cityCoords[normalizedCity] ?? cityCoords['são paulo']!; // Non-null assertion with fallback
}

// Extract coordinates and photos from Gemini conversation
export function extractCoordinatesFromConversation(conversation: any[]): Map<string, Coordinates> {
  const coordinatesMap = new Map<string, Coordinates>();
  
  conversation.forEach(message => {
    if (message.parts) {
      message.parts.forEach((part: any) => {
        if (part.functionResponse?.name === 'get_location_coordinates') {
          const response = part.functionResponse.response;
          if (response.success && response.coordinates) {
            const coords = response.coordinates;
            const key = `${coords.latitude}_${coords.longitude}`;
            coordinatesMap.set(key, coords);
          }
        }
      });
    }
  });
  
  return coordinatesMap;
} 