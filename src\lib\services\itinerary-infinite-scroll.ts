import { useState, useCallback, useRef, useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import type { TravelItinerary, ItinerarySearchParams } from '@/types/travel';

interface InfiniteScrollConfig {
  pageSize: number;
  enableCache: boolean;
  threshold: number;
  rootMargin: string;
  prefetchThreshold: number;
}

interface InfiniteScrollState {
  items: TravelItinerary[];
  hasMore: boolean;
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  cacheStats: {
    fromCache: boolean;
    hitRate: number;
    totalQueries: number;
  };
}

const DEFAULT_CONFIG: InfiniteScrollConfig = {
  pageSize: 12,
  enableCache: true,
  threshold: 0.1,
  rootMargin: '100px',
  prefetchThreshold: 3, // Start loading when 3 items from the end
};

export class ItineraryInfiniteScrollService {
  private state: InfiniteScrollState;
  private config: InfiniteScrollConfig;
  private filters: ItinerarySearchParams;
  private abortController: AbortController | null = null;
  private loadingPromise: Promise<void> | null = null;
  private cacheHits = 0;
  private totalQueries = 0;

  constructor(
    filters: ItinerarySearchParams,
    config: Partial<InfiniteScrollConfig> = {}
  ) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.filters = { ...filters, limit: this.config.pageSize };
    this.state = {
      items: [],
      hasMore: true,
      isLoading: false,
      isLoadingMore: false,
      error: null,
      totalCount: 0,
      currentPage: 0,
      cacheStats: {
        fromCache: false,
        hitRate: 0,
        totalQueries: 0,
      },
    };
  }

  getState(): InfiniteScrollState {
    return { ...this.state };
  }

  updateFilters(newFilters: ItinerarySearchParams): void {
    this.filters = { ...newFilters, limit: this.config.pageSize };
    this.reset();
  }

  updateConfig(newConfig: Partial<InfiniteScrollConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.filters.limit = this.config.pageSize;
  }

  reset(): void {
    this.abortCurrentRequest();
    this.state = {
      ...this.state,
      items: [],
      hasMore: true,
      isLoading: false,
      isLoadingMore: false,
      error: null,
      currentPage: 0,
    };
  }

  async loadInitial(): Promise<InfiniteScrollState> {
    if (this.loadingPromise) {
      await this.loadingPromise;
      return this.getState();
    }

    this.loadingPromise = this._loadPage(0, true);
    await this.loadingPromise;
    this.loadingPromise = null;
    
    return this.getState();
  }

  async loadMore(): Promise<InfiniteScrollState> {
    if (!this.state.hasMore || this.state.isLoading || this.state.isLoadingMore || this.loadingPromise) {
      return this.getState();
    }

    const nextPage = this.state.currentPage + 1;
    this.loadingPromise = this._loadPage(nextPage, false);
    await this.loadingPromise;
    this.loadingPromise = null;
    
    return this.getState();
  }

  private async _loadPage(page: number, isInitial: boolean): Promise<void> {
    const isFirstLoad = isInitial && page === 0;
    
    this.state.isLoading = isFirstLoad;
    this.state.isLoadingMore = !isFirstLoad;
    this.state.error = null;

    this.abortCurrentRequest();
    this.abortController = new AbortController();

    try {
      const searchParams: ItinerarySearchParams = {
        ...this.filters,
        offset: page * this.config.pageSize,
        limit: this.config.pageSize,
      };

      this.totalQueries++;
      
      let result;
      if (this.config.enableCache) {
        // Usar API de busca com cache
        const response = await fetch('/api/itineraries/search', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(searchParams)
        });

        const apiResult = await response.json();
        if (!apiResult.success) {
          throw new Error(apiResult.error || 'Erro ao carregar itinerários');
        }

        result = {
          data: apiResult.data,
          fromCache: apiResult.fromCache
        };

        if (result.fromCache) {
          this.cacheHits++;
        }

        this.state.cacheStats = {
          fromCache: result.fromCache,
          hitRate: this.totalQueries > 0 ? this.cacheHits / this.totalQueries : 0,
          totalQueries: this.totalQueries,
        };
      } else {
        // Usar API de busca sem cache (via GET)
        const queryParams = new URLSearchParams();
        Object.entries(searchParams).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              queryParams.set(key, value.join(','));
            } else {
              queryParams.set(key, String(value));
            }
          }
        });

        const response = await fetch(`/api/itineraries/search?${queryParams}`);
        const apiResult = await response.json();

        if (!apiResult.success) {
          throw new Error(apiResult.error || 'Erro ao carregar itinerários');
        }

        result = { data: apiResult.data, fromCache: false };
      }

      // Check if request was aborted
      if (this.abortController?.signal.aborted) {
        return;
      }

      const newItems = result.data;
      const hasMore = newItems.length === this.config.pageSize;

      if (isInitial) {
        this.state.items = newItems;
        this.state.totalCount = newItems.length;
      } else {
        // Remove duplicates based on ID
        const existingIds = new Set(this.state.items.map(item => item.id));
        const uniqueNewItems = newItems.filter(item => !existingIds.has(item.id));
        
        this.state.items = [...this.state.items, ...uniqueNewItems];
        this.state.totalCount = this.state.items.length;
      }

      this.state.hasMore = hasMore;
      this.state.currentPage = page;
      
    } catch (error) {
      if (this.abortController?.signal.aborted) {
        return;
      }
      
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      this.state.error = errorMessage;
      console.error('Erro ao carregar página:', error);
    } finally {
      this.state.isLoading = false;
      this.state.isLoadingMore = false;
      this.abortController = null;
    }
  }

  private abortCurrentRequest(): void {
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
  }

  // Method to prefetch next page
  async prefetch(): Promise<void> {
    if (!this.state.hasMore || this.state.isLoading || this.state.isLoadingMore) {
      return;
    }

    // Only prefetch if we're close to the end
    const remainingItems = this.state.items.length - this.state.currentPage * this.config.pageSize;
    if (remainingItems > this.config.prefetchThreshold) {
      return;
    }

    try {
      await this.loadMore();
    } catch (error) {
      // Prefetch errors should be silent
      console.warn('Prefetch failed:', error);
    }
  }

  destroy(): void {
    this.abortCurrentRequest();
    this.loadingPromise = null;
  }
}

// React Hook para usar o serviço
export function useInfiniteItineraries(
  filters: ItinerarySearchParams,
  config: Partial<InfiniteScrollConfig> = {}
) {
  const serviceRef = useRef<ItineraryInfiniteScrollService | null>(null);
  const [state, setState] = useState<InfiniteScrollState>(() => {
    const service = new ItineraryInfiniteScrollService(filters, config);
    serviceRef.current = service;
    return service.getState();
  });

  // Create intersection observer for infinite scroll
  const { ref: loadMoreRef, inView } = useInView({
    threshold: config.threshold || DEFAULT_CONFIG.threshold,
    rootMargin: config.rootMargin || DEFAULT_CONFIG.rootMargin,
    triggerOnce: false,
  });

  // Update state when service state changes
  const updateState = useCallback(() => {
    if (serviceRef.current) {
      setState(serviceRef.current.getState());
    }
  }, []);

  // Load initial data
  const loadInitial = useCallback(async () => {
    if (serviceRef.current) {
      await serviceRef.current.loadInitial();
      updateState();
    }
  }, [updateState]);

  // Load more data
  const loadMore = useCallback(async () => {
    if (serviceRef.current) {
      await serviceRef.current.loadMore();
      updateState();
    }
  }, [updateState]);

  // Update filters
  const updateFilters = useCallback((newFilters: ItinerarySearchParams) => {
    if (serviceRef.current) {
      serviceRef.current.updateFilters(newFilters);
      updateState();
      loadInitial();
    }
  }, [updateState, loadInitial]);

  // Update config
  const updateConfig = useCallback((newConfig: Partial<InfiniteScrollConfig>) => {
    if (serviceRef.current) {
      serviceRef.current.updateConfig(newConfig);
      updateState();
    }
  }, [updateState]);

  // Reset data
  const reset = useCallback(() => {
    if (serviceRef.current) {
      serviceRef.current.reset();
      updateState();
    }
  }, [updateState]);

  // Prefetch next page
  const prefetch = useCallback(() => {
    if (serviceRef.current) {
      serviceRef.current.prefetch();
    }
  }, []);

  // Load more when in view
  useEffect(() => {
    if (inView && state.hasMore && !state.isLoadingMore && !state.isLoading) {
      loadMore();
    }
  }, [inView, state.hasMore, state.isLoadingMore, state.isLoading, loadMore]);

  // Load initial data on mount
  useEffect(() => {
    loadInitial();
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (serviceRef.current) {
        serviceRef.current.destroy();
      }
    };
  }, []);

  return {
    ...state,
    loadMoreRef,
    loadInitial,
    loadMore,
    updateFilters,
    updateConfig,
    reset,
    prefetch,
    // Computed properties
    isEmpty: state.items.length === 0 && !state.isLoading,
    isInitialLoading: state.isLoading && state.items.length === 0,
    canLoadMore: state.hasMore && !state.isLoading && !state.isLoadingMore,
  };
} 