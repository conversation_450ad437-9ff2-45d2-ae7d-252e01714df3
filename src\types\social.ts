import type { Enums } from './database';

// =====================================================
// TIPOS ESSENCIAIS - FEED SIMPLES 2025
// =====================================================

export type PostType = Enums<'post_type'>;
export type PostVisibility = Enums<'post_visibility'>;

// =====================================================
// USER TYPES - ESSENCIAL
// =====================================================
export interface SocialUser {
  id: string;
  username: string;
  displayName: string | null;
  avatarUrl: string | null;
  verified: boolean;
  bio?: string | null;
  location?: string | null;
  followersCount?: number;
  followingCount?: number;
  postsCount?: number;
}

// =====================================================
// MEDIA & LOCATION - ESSENCIAL
// =====================================================
export interface MediaItem {
  type: 'image' | 'video' | 'audio';
  url: string;
  thumbnail?: string;
  width?: number;
  height?: number;
  duration?: number;
  alt?: string;
}

export interface LocationData {
  lat: number;
  lng: number;
  name?: string;
  address?: string;
  placeId?: string;
}

// =====================================================
// POST TYPE - ESSENCIAL
// =====================================================
export interface FeedPost {
  id: string;
  user: SocialUser;
  content: string | null;
  type: PostType;
  visibility: PostVisibility;
  mediaUrls: MediaItem[];
  location?: LocationData | null;
  tags: string[];
  mentions: string[];
  likesCount: number;
  commentsCount: number;
  sharesCount: number;
  viewsCount: number;
  isPinned: boolean;
  isLiked: boolean;
  isShared: boolean;
  isSaved?: boolean;
  userReaction?: ReactionType;
  reactions?: PostReactionSummary;
  createdAt: string;
  updatedAt: string;
}

// =====================================================
// FORM DATA - ESSENCIAL
// =====================================================
export interface CreatePostData {
  content?: string;
  type?: PostType;
  visibility?: PostVisibility;
  mediaUrls?: MediaItem[];
  location?: LocationData;
  tags?: string[];
  mentions?: string[];
}

// =====================================================
// NOTIFICATIONS - SISTEMA SOCIAL
// =====================================================
export type SocialNotificationType =
  | 'like'           // Alguém curtiu seu post
  | 'comment'        // Alguém comentou seu post
  | 'follow'         // Alguém começou a seguir você
  | 'mention'        // Alguém te mencionou
  | 'reply'          // Alguém respondeu seu comentário
  | 'story_view';    // Alguém viu seu story

export interface SocialNotification {
  id: string;
  user_id: string;           // Quem vai receber
  actor_id: string;          // Quem fez a ação
  actor: SocialUser;         // Dados do usuário que fez a ação (user_profiles)
  type: SocialNotificationType;
  post_id?: string;
  comment_id?: string;
  story_id?: string;
  content?: string;          // Conteúdo adicional (ex: texto do comentário)
  read: boolean;
  created_at: string;
}

export interface NotificationGroup {
  type: SocialNotificationType;
  count: number;
  latest_actor: SocialUser;
  other_actors: SocialUser[];
  post_id?: string;
  created_at: string;
  read: boolean;
}

// =====================================================
// REACTIONS - SISTEMA AVANÇADO
// =====================================================
export type ReactionType = 'like' | 'love' | 'laugh' | 'wow' | 'sad' | 'angry';

export interface PostReaction {
  id: string;
  post_id: string;
  user_id: string;
  reaction_type: ReactionType;
  created_at: string;
}

export interface PostReactionSummary {
  [key in ReactionType]: {
    count: number;
    users: SocialUser[];
  }
}

// =====================================================
// HOOK RETURN - ESSENCIAL
// =====================================================
export interface UseFeedReturn {
  posts: FeedPost[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  likePost: (postId: string) => Promise<void>;
  createPost: (data: CreatePostData) => Promise<void>;
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
}

export interface UseNotificationsReturn {
  notifications: SocialNotification[];
  groupedNotifications: NotificationGroup[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  refresh: () => Promise<void>;
}

// =====================================================
// COMPONENT PROPS - ESSENCIAL
// =====================================================
export interface PostCardProps {
  post: FeedPost;
  onLike?: (postId: string) => void;
  onUserClick?: (userId: string) => void;
  showActions?: boolean;
  compact?: boolean;
  className?: string;
  isFirstPost?: boolean;
}

// =====================================================
// STORY TYPES - FUNCIONAL
// =====================================================
export interface Story {
  id: string;
  user: SocialUser;
  content: string | null;
  mediaUrl: string;
  mediaType: 'image' | 'video';
  createdAt: string;
  viewsCount: number;
  isViewed?: boolean;
}

export interface CreateStoryData {
  content?: string;
  mediaUrl: string;
  mediaType: 'image' | 'video';
}
