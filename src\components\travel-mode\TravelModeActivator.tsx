'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MapPin, Plane, Clock } from 'lucide-react';
import { format, isAfter, isBefore } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface TravelModeActivatorProps {
  itineraryId: string;
  startDate: string | undefined;
  endDate: string | undefined;
  isOwner: boolean;
}

export default function TravelModeActivator({ 
  itineraryId, 
  startDate, 
  endDate, 
  isOwner 
}: TravelModeActivatorProps) {
  const [isActivating, setIsActivating] = useState(false);
  const router = useRouter();

  // Verificar se está na data da viagem
  const now = new Date();
  const start = startDate ? new Date(startDate) : null;
  const end = endDate ? new Date(endDate) : null;
  
  let travelStatus: 'upcoming' | 'active' | 'ended' | 'no-dates' = 'no-dates';
  
  if (start && end) {
    if (isBefore(now, start)) {
      travelStatus = 'upcoming';
    } else if (isAfter(now, end)) {
      travelStatus = 'ended';
    } else {
      travelStatus = 'active';
    }
  }

  const handleActivate = async () => {
    setIsActivating(true);
    try {
      // Se não está ativo, tentar ativar primeiro
      if (travelStatus !== 'active') {
        const response = await fetch(`/api/travel-mode/${itineraryId}/activate`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });

        const result = await response.json();

        if (!result.success) {
          console.error('Erro ao ativar travel mode:', result.error);
          // Mesmo assim, navegar para a página (pode ser que já esteja ativo)
        }
      }

      // Usar window.location para evitar problemas de prefetch do Next.js
      window.location.href = `/travel-mode/${itineraryId}`;
    } catch (error) {
      console.error('Erro ao ativar modo viagem:', error);
      // Em caso de erro, ainda tentar navegar
      window.location.href = `/travel-mode/${itineraryId}`;
    } finally {
      setIsActivating(false);
    }
  };

  // Apenas mostrar para o proprietário
  if (!isOwner) {
    return null;
  }

  const getStatusInfo = () => {
    switch (travelStatus) {
      case 'active':
        return {
          badge: <Badge className="bg-green-500 text-white">Em Viagem</Badge>,
          buttonText: 'Acessar Modo Viagem',
          buttonVariant: 'default' as const,
          description: 'Sua viagem está ativa! Acesse o modo viagem para acompanhar em tempo real.',
        };
      case 'upcoming':
        return {
          badge: <Badge variant="outline" className="border-blue-500 text-blue-600">Próxima Viagem</Badge>,
          buttonText: 'Preparar Modo Viagem',
          buttonVariant: 'outline' as const,
          description: start ? `Viagem inicia em ${format(start, 'dd/MM/yyyy', { locale: ptBR })}` : '',
        };
      case 'ended':
        return {
          badge: <Badge variant="secondary">Viagem Finalizada</Badge>,
          buttonText: 'Ver Histórico da Viagem',
          buttonVariant: 'outline' as const,
          description: end ? `Viagem finalizada em ${format(end, 'dd/MM/yyyy', { locale: ptBR })}` : '',
        };
      default:
        return {
          badge: <Badge variant="outline">Sem Datas Definidas</Badge>,
          buttonText: 'Simular Modo Viagem',
          buttonVariant: 'outline' as const,
          description: 'Configure as datas da viagem para ativação automática.',
        };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4 space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Plane className="h-5 w-5 text-blue-600" />
          <span className="font-medium text-blue-900">Modo Viagem</span>
          {statusInfo.badge}
        </div>
        
        {travelStatus === 'active' && (
          <div className="flex items-center gap-1 text-green-600 text-sm">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span>Ativo agora</span>
          </div>
        )}
      </div>

      {statusInfo.description && (
        <p className="text-sm text-muted-foreground">{statusInfo.description}</p>
      )}

      <div className="flex items-center gap-2">
        <Button
          onClick={handleActivate}
          disabled={isActivating}
          variant={statusInfo.buttonVariant}
          size="sm"
          className={travelStatus === 'active' ? 'bg-green-600 hover:bg-green-700' : ''}
        >
          {isActivating ? (
            <>
              <Clock className="h-4 w-4 mr-2 animate-spin" />
              Carregando...
            </>
          ) : (
            <>
              <MapPin className="h-4 w-4 mr-2" />
              {statusInfo.buttonText}
            </>
          )}
        </Button>

        {travelStatus === 'active' && (
          <span className="text-xs text-green-700 bg-green-100 px-2 py-1 rounded-full">
            Acompanhe sua viagem em tempo real
          </span>
        )}
      </div>
    </div>
  );
} 