'use client';

import React, { useState } from 'react';
import { Bell } from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';
import { NotificationCenter } from './NotificationCenter';

// =====================================================
// NOTIFICATION BADGE - COMPONENTE COMPACTO E EFICIENTE
// =====================================================

interface NotificationBadgeProps {
  className?: string;
  showLabel?: boolean;
}

export function NotificationBadge({ className = '', showLabel = false }: NotificationBadgeProps) {
  const { unreadCount, loading } = useNotifications();
  const [isOpen, setIsOpen] = useState(false);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  return (
    <>
      {/* Badge Button */}
      <button
        onClick={handleToggle}
        className={`relative p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors ${className}`}
        title="Notificações"
        disabled={loading}
      >
        <Bell className={`w-6 h-6 text-gray-600 dark:text-gray-300 ${loading ? 'animate-pulse' : ''}`} />
        
        {/* Unread Count Badge */}
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-medium rounded-full min-w-[1.25rem] h-5 flex items-center justify-center px-1">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}

        {/* Label (opcional) */}
        {showLabel && (
          <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
            Notificações
          </span>
        )}
      </button>

      {/* Notification Center */}
      <NotificationCenter 
        isOpen={isOpen}
        onClose={handleClose}
      />
    </>
  );
}
