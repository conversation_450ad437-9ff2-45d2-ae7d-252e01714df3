'use client';

import { useState, useEffect, useTransition, useMemo, useCallback } from 'react';
import { FixedSizeGrid as Grid } from 'react-window';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  MapPin, Calendar, Heart, Bookmark, Eye, Share2, 
  Edit3, Trash2, Plus, ExternalLink, CheckSquare,
  Settings
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { 
  getUserItineraries
} from '@/lib/actions/travel-actions';
import type { TravelItinerary, ItineraryStatus } from '@/types/travel';
import { TRIP_TYPE_LABELS } from '@/types/travel';
import DeleteItineraryDialog from './DeleteItineraryDialog';
import BulkActionsBar from './BulkActionsBar';
import CreateTemplateDialog from './CreateTemplateDialog';

// Virtualization settings
const VIRTUALIZATION_THRESHOLD = 50; // Start virtualizing after 50 items
const CARD_HEIGHT = 400; // Approximate height of each card
const CARDS_PER_ROW = 3; // Default cards per row

interface UserItinerariesListProps {
  status?: ItineraryStatus | 'traveling' | 'planning' | 'completed';
}

interface ItineraryCardProps {
  itinerary: TravelItinerary & { author: any };
  isSelected: boolean;
  onSelectionChange: (selected: boolean) => void;
  onDeleted: () => void;
  onTemplateCreated: () => void;
  selectionMode: boolean;
}

const STATUS_LABELS = {
  public: 'Público',
  private: 'Privado',
  friends: 'Amigos',
  traveling: 'em viagem',
  planning: 'planejando',
  completed: 'concluído',
};

const STATUS_COLORS = {
  public: 'default',
  private: 'outline',
  friends: 'secondary',
  traveling: 'default',
  planning: 'outline', 
  completed: 'secondary',
} as const;

function ItineraryCard({ 
  itinerary, 
  isSelected, 
  onSelectionChange, 
  onDeleted,
  onTemplateCreated,
  selectionMode 
}: ItineraryCardProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [templateDialogOpen, setTemplateDialogOpen] = useState(false);

  const handleShare = async () => {
    const url = `${window.location.origin}/itineraries/${itinerary.id}`;
    
    if (navigator.share && typeof navigator.share === 'function') {
      await navigator.share({
        title: itinerary.title,
        url
      });
    } else {
      navigator.clipboard.writeText(url);
    }
  };

  const handleViewDetails = async () => {
    // No analytics tracking for view details
  };

  const handleEdit = async () => {
    // No analytics tracking for edit
  };

  const handleCreateTemplate = async () => {
    // No analytics tracking for create template
    setTemplateDialogOpen(true);
  };

  return (
    <>
      <Card className={`group hover:shadow-lg transition-all duration-200 overflow-hidden ${
        isSelected ? 'ring-2 ring-primary' : ''
      }`}>
        <div className="relative aspect-video overflow-hidden">
          {itinerary.cover_image_url ? (
            <Image
              src={itinerary.cover_image_url}
              alt={itinerary.title}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              className="object-cover group-hover:scale-105 transition-transform duration-200"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-primary/20 to-primary/40 flex items-center justify-center">
              <MapPin className="h-12 w-12 text-primary/60" />
            </div>
          )}
          
          {/* Checkbox de seleção */}
          {selectionMode && (
            <div className="absolute top-3 left-3 z-10">
              <Checkbox
                checked={isSelected}
                onCheckedChange={onSelectionChange}
                className="bg-white/90 border-white data-[state=checked]:bg-primary data-[state=checked]:border-primary"
              />
            </div>
          )}
          
          {/* Badge de status */}
          <div className={`absolute top-3 ${selectionMode ? 'left-12' : 'left-3'}`}>
            <Badge variant={STATUS_COLORS[itinerary.status]}>
              {STATUS_LABELS[itinerary.status]}
            </Badge>
          </div>

          {/* Tipo de viagem */}
          <div className="absolute top-3 right-3">
            <Badge variant="outline" className="bg-white/90 text-black">
              {TRIP_TYPE_LABELS[itinerary.trip_type]}
            </Badge>
          </div>
        </div>

        <CardContent className="p-4 space-y-4">
          {/* Título e descrição */}
          <div>
            <Link href={`/itineraries/${itinerary.id}`} onClick={handleViewDetails}>
              <h3 className="font-semibold text-lg mb-2 group-hover:text-primary transition-colors line-clamp-2">
                {itinerary.title}
              </h3>
            </Link>

            {itinerary.description && (
              <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                {itinerary.description}
              </p>
            )}
          </div>

          {/* Destinos */}
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            <span className="text-sm text-muted-foreground truncate">
              {itinerary.cities.slice(0, 3).join(', ')}
              {itinerary.cities.length > 3 && ` +${itinerary.cities.length - 3}`}
            </span>
          </div>

          {/* Período */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              <span>{itinerary.total_days} dia{itinerary.total_days !== 1 ? 's' : ''}</span>
            </div>
            
            {itinerary.estimated_cost_min && itinerary.estimated_cost_max && (
              <span className="font-medium text-foreground">
                R$ {itinerary.estimated_cost_min} - R$ {itinerary.estimated_cost_max}
              </span>
            )}
          </div>

          {/* Estatísticas sociais */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                <Heart className="h-4 w-4" />
                <span>{itinerary.likes_count}</span>
              </div>
              <div className="flex items-center gap-1">
                <Bookmark className="h-4 w-4" />
                <span>{itinerary.saves_count}</span>
              </div>
              <div className="flex items-center gap-1">
                <Eye className="h-4 w-4" />
                <span>{itinerary.views_count}</span>
              </div>
            </div>
          </div>

          {/* Ações */}
          <div className="flex items-center gap-2 pt-2 border-t">
            <Button asChild variant="outline" size="sm" className="flex-1">
              <Link href={`/itineraries/${itinerary.id}`} onClick={handleViewDetails}>
                <ExternalLink className="h-4 w-4 mr-1" />
                Ver Detalhes
              </Link>
            </Button>
            
            <Button asChild variant="outline" size="sm">
              <Link href={`/itineraries/${itinerary.id}/edit`} onClick={handleEdit}>
                <Edit3 className="h-4 w-4" />
              </Link>
            </Button>

            <Button variant="outline" size="sm" onClick={handleCreateTemplate}>
              <Settings className="h-4 w-4" />
            </Button>
            
            <Button variant="outline" size="sm" onClick={handleShare}>
              <Share2 className="h-4 w-4" />
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setDeleteDialogOpen(true)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      <DeleteItineraryDialog
        itinerary={itinerary}
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onDeleted={onDeleted}
      />

      <CreateTemplateDialog
        itinerary={itinerary}
        open={templateDialogOpen}
        onOpenChange={setTemplateDialogOpen}
        onTemplateCreated={onTemplateCreated}
      />
    </>
  );
}

// Virtualized grid cell component for large lists
function VirtualizedCell({ 
  columnIndex, 
  rowIndex, 
  style, 
  data 
}: {
  columnIndex: number;
  rowIndex: number;
  style: React.CSSProperties;
  data: {
    itineraries: (TravelItinerary & { author: any })[];
    selectedIds: Set<string>;
    selectionMode: boolean;
    onSelectionChange: (id: string, selected: boolean) => void;
    onItineraryDeleted: () => void;
    onTemplateCreated: () => void;
  };
}) {
  const { itineraries, selectedIds, selectionMode, onSelectionChange, onItineraryDeleted, onTemplateCreated } = data;
  const itemIndex = rowIndex * CARDS_PER_ROW + columnIndex;
  const itinerary = itineraries[itemIndex];

  if (!itinerary) {
    return <div style={style} />;
  }

  return (
    <div style={{ ...style, padding: '12px' }}>
      <ItineraryCard
        key={itinerary.id}
        itinerary={itinerary}
        isSelected={selectedIds.has(itinerary.id)}
        onSelectionChange={(selected) => onSelectionChange(itinerary.id, selected)}
        onDeleted={onItineraryDeleted}
        onTemplateCreated={onTemplateCreated}
        selectionMode={selectionMode}
      />
    </div>
  );
}

export default function UserItinerariesList({ status }: UserItinerariesListProps) {
  const [itineraries, setItineraries] = useState<(TravelItinerary & { author: any })[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();
  
  // Estados para seleção múltipla
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
  const [selectionMode, setSelectionMode] = useState(false);

  const selectedItineraries = itineraries.filter(itinerary => 
    selectedIds.has(itinerary.id)
  );

  // Memoized calculation for virtualization
  const shouldVirtualize = itineraries.length > VIRTUALIZATION_THRESHOLD;

  const loadItineraries = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await getUserItineraries(status);
      
      if (result.success) {
        setItineraries(result.data);
      } else {
        setError(result.error || 'Erro ao carregar roteiros');
      }
    } catch (err) {
      setError('Erro ao carregar roteiros');
      console.error('Erro ao carregar roteiros do usuário:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    startTransition(() => {
      loadItineraries();
    });
  }, [status]);

  const handleSelectionChange = useCallback((itineraryId: string, selected: boolean) => {
    const newSelection = new Set(selectedIds);
    if (selected) {
      newSelection.add(itineraryId);
    } else {
      newSelection.delete(itineraryId);
    }
    setSelectedIds(newSelection);
  }, [selectedIds]);

  const handleSelectAll = useCallback(() => {
    if (selectedIds.size === itineraries.length) {
      setSelectedIds(new Set());
    } else {
      setSelectedIds(new Set(itineraries.map(i => i.id)));
    }
  }, [selectedIds.size, itineraries]);

  const handleClearSelection = useCallback(() => {
    setSelectedIds(new Set());
    setSelectionMode(false);
  }, []);

  const handleBulkActionComplete = useCallback(() => {
    loadItineraries();
  }, []);

  const handleItineraryDeleted = useCallback(() => {
    loadItineraries();
  }, []);

  const handleTemplateCreated = useCallback(() => {
    // Optionally refresh or show success message
    console.log('Template created successfully');
  }, []);

  // Memoized grid data for virtualization - defined after callback functions
  const gridData = useMemo(() => ({
    itineraries,
    selectedIds,
    selectionMode,
    onSelectionChange: handleSelectionChange,
    onItineraryDeleted: handleItineraryDeleted,
    onTemplateCreated: handleTemplateCreated
  }), [itineraries, selectedIds, selectionMode, handleSelectionChange, handleItineraryDeleted, handleTemplateCreated]);

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 mb-4">⚠️ {error}</div>
        <Button onClick={() => loadItineraries()}>
          Tentar Novamente
        </Button>
      </div>
    );
  }

  if (isLoading || isPending) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }, (_, i) => (
          <Card key={i} className="animate-pulse">
            <div className="aspect-video bg-muted rounded-t-lg" />
            <CardContent className="p-4 space-y-3">
              <div className="h-4 bg-muted rounded w-3/4" />
              <div className="h-3 bg-muted rounded w-1/2" />
              <div className="flex gap-2">
                <div className="h-5 bg-muted rounded w-16" />
                <div className="h-5 bg-muted rounded w-20" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (itineraries.length === 0) {
    return (
      <div className="text-center py-12">
        <MapPin className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">
          {status ? `Nenhum roteiro ${STATUS_LABELS[status].toLowerCase()} encontrado` : 'Nenhum roteiro encontrado'}
        </h3>
        <p className="text-muted-foreground mb-4">
          {status === 'private' 
            ? 'Que tal começar planejando sua próxima aventura?'
            : 'Crie seu primeiro roteiro e comece a explorar o mundo.'
          }
        </p>
        <Button asChild>
          <Link href="/itineraries/create">
            <Plus className="h-4 w-4 mr-2" />
            Criar Roteiro
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        {/* Stats e controles */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <p className="text-sm text-muted-foreground">
              {itineraries.length} roteiro{itineraries.length !== 1 ? 's' : ''} 
              {status && ` ${STATUS_LABELS[status].toLowerCase()}`}
              {shouldVirtualize && ' (Lista Otimizada)'}
            </p>
            
            {itineraries.length > 0 && (
              <div className="flex items-center gap-2">
                {!selectionMode ? (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setSelectionMode(true)}
                  >
                    <CheckSquare className="h-4 w-4 mr-2" />
                    Selecionar
                  </Button>
                ) : (
                  <div className="flex items-center gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={handleSelectAll}
                    >
                      {selectedIds.size === itineraries.length ? 'Desmarcar Todos' : 'Selecionar Todos'}
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={handleClearSelection}
                    >
                      Cancelar
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
          
          <div className="flex gap-2">
            <Button asChild variant="outline" size="sm">
              <Link href="/templates">
                <Settings className="h-4 w-4 mr-2" />
                Templates
              </Link>
            </Button>
            <Button asChild variant="outline" size="sm">
              <Link href="/itineraries/create">
                <Plus className="h-4 w-4 mr-2" />
                Novo Roteiro
              </Link>
            </Button>
          </div>
        </div>

        {/* Grid de roteiros - Virtualizado ou Normal */}
        {shouldVirtualize ? (
          <div className="h-[800px] w-full">
            <Grid
              columnCount={CARDS_PER_ROW}
              columnWidth={400}
              height={800}
              rowCount={Math.ceil(itineraries.length / CARDS_PER_ROW)}
              rowHeight={CARD_HEIGHT}
              width={1200}
              itemData={gridData}
            >
              {VirtualizedCell}
            </Grid>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {itineraries.map((itinerary) => (
              <ItineraryCard 
                key={itinerary.id} 
                itinerary={itinerary}
                isSelected={selectedIds.has(itinerary.id)}
                onSelectionChange={(selected) => handleSelectionChange(itinerary.id, selected)}
                onDeleted={handleItineraryDeleted}
                onTemplateCreated={handleTemplateCreated}
                selectionMode={selectionMode}
              />
            ))}
          </div>
        )}
      </div>

      {/* Barra de ações em lote */}
      <BulkActionsBar
        selectedItineraries={selectedItineraries}
        onClearSelection={handleClearSelection}
        onBulkActionComplete={handleBulkActionComplete}
      />
    </>
  );
} 