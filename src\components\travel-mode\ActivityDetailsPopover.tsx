"use client";

import React from 'react';
import * as Popover from '@radix-ui/react-popover';
import * as Separator from '@radix-ui/react-separator';

import { 
  MapPin, 
  Clock, 
  Calendar,
  Info,
  Star,
  DollarSign,
  Users,
  Phone,
  Globe,
  Navigation,
  X,
  CheckCircle,
  AlertCircle,
  PlayCircle,
  SkipForward
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Função para formatar tempo (copiada do TravelModeDashboard)
function formatTimeFromString(timeString: string | null | undefined): string {
  if (!timeString) return '';

  try {
    // timeString vem como "09:30:00" do PostgreSQL
    const timeParts = timeString.split(':');
    if (timeParts.length >= 2 && timeParts[0] && timeParts[1]) {
      const hours = timeParts[0].padStart(2, '0');
      const minutes = timeParts[1].padStart(2, '0');
      return `${hours}:${minutes}`;
    }
    return timeString;
  } catch {
    return '';
  }
}

interface ActivityDetailsPopoverProps {
  activity: {
    id: string;
    title: string;
    description?: string;
    location_name?: string;
    start_time?: string;
    end_time?: string;
    day_number: number;
    status?: 'pending' | 'in_progress' | 'completed' | 'skipped';
    started_at?: string;
    completed_at?: string;
    notes?: string;
    // Dados adicionais que podem vir do banco
    location_data?: {
      lat: number;
      lng: number;
      address?: string;
      place_id?: string;
    };
    activity_type?: string;
    estimated_duration?: number;
    estimated_cost?: number;
    difficulty_level?: 'easy' | 'moderate' | 'hard';
    accessibility_info?: string;
    contact_info?: {
      phone?: string;
      website?: string;
      email?: string;
    };
    opening_hours?: {
      [key: string]: string;
    };
    rating?: number;
    reviews_count?: number;
    tags?: string[];
    requirements?: string[];
    tips?: string[];
  };
  children: React.ReactNode;
  onGetDirections?: (activityId: string) => void;
  onMarkCompleted?: (activityId: string, notes?: string) => Promise<void>;
  onStartActivity?: (activityId: string) => Promise<void>;
  onSkipActivity?: (activityId: string, reason?: string) => Promise<void>;
}

export function ActivityDetailsPopover({
  activity,
  children,
  onGetDirections,
  onMarkCompleted,
  onStartActivity,
  onSkipActivity
}: ActivityDetailsPopoverProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [actionLoading, setActionLoading] = React.useState<string | null>(null);

  // Usar dados da atividade diretamente com dados simulados consistentes
  const detailedActivity = React.useMemo(() => {
    // Função para gerar dados consistentes baseados no ID da atividade
    const generateConsistentData = (activityId: string) => {
      let hash = 0;
      for (let i = 0; i < activityId.length; i++) {
        const char = activityId.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
      }

      const seed = Math.abs(hash);

      return {
        estimated_duration: 60 + (seed % 120),
        estimated_cost: 20 + (seed % 100),
        difficulty_level: ['easy', 'moderate', 'hard'][seed % 3] as 'easy' | 'moderate' | 'hard',
        activity_type: ['cultural', 'gastronomy', 'entertainment', 'shopping'][seed % 4],
        rating: (3 + ((seed % 20) / 10)).toFixed(1),
        reviews_count: 50 + (seed % 500),
      };
    };

    const consistentData = generateConsistentData(activity.id);

    return {
      ...activity,
      estimated_duration: activity.estimated_duration || consistentData.estimated_duration,
      estimated_cost: activity.estimated_cost || consistentData.estimated_cost,
      difficulty_level: activity.difficulty_level || consistentData.difficulty_level,
      activity_type: activity.activity_type || consistentData.activity_type,
      rating: activity.rating || consistentData.rating,
      reviews_count: activity.reviews_count || consistentData.reviews_count,
      contact_info: activity.contact_info || {
        phone: '+55 11 1234-5678',
        website: 'https://example.com'
      },
      accessibility_info: activity.accessibility_info || 'Acessível para cadeirantes',
      tags: activity.tags || ['popular', 'imperdível', 'família'],
      requirements: activity.requirements || ['Documento de identidade'],
      tips: activity.tips || ['Chegue cedo para evitar filas', 'Leve protetor solar']
    };
  }, [activity]);

  const handleAction = async (action: string, handler?: (...args: any[]) => Promise<void>, ...args: any[]) => {
    if (!handler || actionLoading) return;

    setActionLoading(action);
    try {
      await handler(...args);
      setIsOpen(false); // Fechar popover após ação
    } catch (error) {
      console.error(`Erro na ação ${action}:`, error);
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusInfo = () => {
    const status = detailedActivity.status || 'pending';
    const statusConfig = {
      pending: { 
        label: 'Pendente', 
        color: 'text-gray-600 bg-gray-100 dark:text-gray-300 dark:bg-gray-800',
        icon: AlertCircle
      },
      in_progress: { 
        label: 'Em Andamento', 
        color: 'text-blue-600 bg-blue-100 dark:text-blue-300 dark:bg-blue-800',
        icon: PlayCircle
      },
      completed: { 
        label: 'Concluída', 
        color: 'text-green-600 bg-green-100 dark:text-green-300 dark:bg-green-800',
        icon: CheckCircle
      },
      skipped: { 
        label: 'Pulada', 
        color: 'text-orange-600 bg-orange-100 dark:text-orange-300 dark:bg-orange-800',
        icon: SkipForward
      }
    };
    return statusConfig[status];
  };

  const statusInfo = getStatusInfo();
  const StatusIcon = statusInfo.icon;

  return (
    <Popover.Root open={isOpen} onOpenChange={setIsOpen}>
      <Popover.Trigger asChild>
        {children}
      </Popover.Trigger>
      
      <Popover.Portal>
        <Popover.Content
          className={cn(
            "z-50 w-96 max-w-[95vw] max-h-[90vh] overflow-y-auto",
            "bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800",
            "rounded-xl shadow-xl",
            "data-[state=open]:animate-in data-[state=closed]:animate-out",
            "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
            "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
            "data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2",
            "data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2"
          )}
          side="left"
          align="start"
          sideOffset={8}
        >
          {/* Header */}
          <div className="flex items-start justify-between p-6 pb-4">
            <div className="flex-1 pr-4">
              <h3 className="text-lg font-semibold text-zinc-900 dark:text-white leading-tight">
                {detailedActivity.title}
              </h3>

              <div className="flex items-center gap-2 mt-2">
                <StatusIcon className="w-4 h-4" />
                <span className={cn("px-2 py-1 rounded-full text-xs font-medium", statusInfo.color)}>
                  {statusInfo.label}
                </span>
              </div>
            </div>
            
            <Popover.Close className="p-1 rounded-lg hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors">
              <X className="w-4 h-4 text-zinc-500" />
            </Popover.Close>
          </div>

          <div className="px-6 space-y-4">
            {/* Horário e Duração */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-zinc-600 dark:text-zinc-300">
                <Clock className="w-4 h-4" />
                <span>
                  {detailedActivity.start_time && formatTimeFromString(detailedActivity.start_time)}
                  {detailedActivity.end_time && ` - ${formatTimeFromString(detailedActivity.end_time)}`}
                </span>
              </div>

              {detailedActivity.estimated_duration && (
                <div className="flex items-center gap-2 text-sm text-zinc-600 dark:text-zinc-300">
                  <Calendar className="w-4 h-4" />
                  <span>Duração estimada: {detailedActivity.estimated_duration} minutos</span>
                </div>
              )}
            </div>

            {/* Localização */}
            {detailedActivity.location_name && (
              <div className="space-y-2">
                <div className="flex items-start gap-2 text-sm text-zinc-600 dark:text-zinc-300">
                  <MapPin className="w-4 h-4 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium">{detailedActivity.location_name}</div>
                    {detailedActivity.location_data?.address && (
                      <div className="text-xs text-zinc-500 dark:text-zinc-400 mt-1">
                        {detailedActivity.location_data.address}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            <Separator.Root className="h-px bg-zinc-200 dark:bg-zinc-800" />

            {/* Descrição */}
            {activity.description && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-zinc-900 dark:text-white">
                  <Info className="w-4 h-4" />
                  Descrição
                </div>
                <p className="text-sm text-zinc-600 dark:text-zinc-300 leading-relaxed">
                  {activity.description}
                </p>
              </div>
            )}

            {/* Informações Adicionais */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              {activity.rating && (
                <div className="flex items-center gap-2">
                  <Star className="w-4 h-4 text-yellow-500" />
                  <span className="text-zinc-600 dark:text-zinc-300">
                    {activity.rating}/5 ({activity.reviews_count || 0})
                  </span>
                </div>
              )}

              {activity.estimated_cost && (
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-green-500" />
                  <span className="text-zinc-600 dark:text-zinc-300">
                    R$ {activity.estimated_cost}
                  </span>
                </div>
              )}

              {activity.difficulty_level && (
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4 text-blue-500" />
                  <span className="text-zinc-600 dark:text-zinc-300 capitalize">
                    {activity.difficulty_level === 'easy' ? 'Fácil' : 
                     activity.difficulty_level === 'moderate' ? 'Moderado' : 'Difícil'}
                  </span>
                </div>
              )}

              {activity.activity_type && (
                <div className="flex items-center gap-2">
                  <Info className="w-4 h-4 text-purple-500" />
                  <span className="text-zinc-600 dark:text-zinc-300 capitalize">
                    {activity.activity_type}
                  </span>
                </div>
              )}
            </div>

            {/* Contato */}
            {activity.contact_info && (
              <>
                <Separator.Root className="h-px bg-zinc-200 dark:bg-zinc-800" />
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-zinc-900 dark:text-white">Contato</h4>
                  <div className="space-y-1">
                    {activity.contact_info.phone && (
                      <div className="flex items-center gap-2 text-sm text-zinc-600 dark:text-zinc-300">
                        <Phone className="w-4 h-4" />
                        <a href={`tel:${activity.contact_info.phone}`} className="hover:underline">
                          {activity.contact_info.phone}
                        </a>
                      </div>
                    )}
                    {activity.contact_info.website && (
                      <div className="flex items-center gap-2 text-sm text-zinc-600 dark:text-zinc-300">
                        <Globe className="w-4 h-4" />
                        <a 
                          href={activity.contact_info.website} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="hover:underline"
                        >
                          Website
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}

            {/* Status de Tracking */}
            {(activity.started_at || activity.completed_at || activity.notes) && (
              <>
                <Separator.Root className="h-px bg-zinc-200 dark:bg-zinc-800" />
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-zinc-900 dark:text-white">Histórico</h4>
                  <div className="space-y-1 text-xs text-zinc-500 dark:text-zinc-400">
                    {activity.started_at && (
                      <div>Iniciada em: {new Date(activity.started_at).toLocaleString('pt-BR')}</div>
                    )}
                    {activity.completed_at && (
                      <div>Concluída em: {new Date(activity.completed_at).toLocaleString('pt-BR')}</div>
                    )}
                    {activity.notes && (
                      <div className="italic">"{activity.notes}"</div>
                    )}
                  </div>
                </div>
              </>
            )}

            {/* Ações */}
            <Separator.Root className="h-px bg-zinc-200 dark:bg-zinc-800" />
            <div className="flex flex-wrap gap-2 pb-2">
              {activity.status === 'pending' && onStartActivity && (
                <button 
                  onClick={() => handleAction('start', onStartActivity, activity.id)}
                  disabled={actionLoading === 'start'}
                  className={cn(
                    "inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium",
                    "bg-blue-600 text-white hover:bg-blue-700",
                    "disabled:opacity-50 disabled:cursor-not-allowed",
                    "transition-colors"
                  )}
                >
                  {actionLoading === 'start' ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <PlayCircle className="w-4 h-4" />
                  )}
                  Iniciar
                </button>
              )}

              {activity.status === 'in_progress' && onMarkCompleted && (
                <button 
                  onClick={() => handleAction('complete', onMarkCompleted, activity.id)}
                  disabled={actionLoading === 'complete'}
                  className={cn(
                    "inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium",
                    "bg-green-600 text-white hover:bg-green-700",
                    "disabled:opacity-50 disabled:cursor-not-allowed",
                    "transition-colors"
                  )}
                >
                  {actionLoading === 'complete' ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <CheckCircle className="w-4 h-4" />
                  )}
                  Concluir
                </button>
              )}

              {(activity.status === 'pending' || activity.status === 'in_progress') && onSkipActivity && (
                <button 
                  onClick={() => handleAction('skip', onSkipActivity, activity.id, 'Pulada via detalhes')}
                  disabled={actionLoading === 'skip'}
                  className={cn(
                    "inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium",
                    "bg-orange-600 text-white hover:bg-orange-700",
                    "disabled:opacity-50 disabled:cursor-not-allowed",
                    "transition-colors"
                  )}
                >
                  {actionLoading === 'skip' ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <SkipForward className="w-4 h-4" />
                  )}
                  Pular
                </button>
              )}

              {activity.location_name && onGetDirections && (
                <button 
                  onClick={() => onGetDirections(activity.id)}
                  className={cn(
                    "inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium",
                    "bg-white dark:bg-zinc-800 text-zinc-900 dark:text-white",
                    "border border-zinc-200 dark:border-zinc-700",
                    "hover:bg-zinc-50 dark:hover:bg-zinc-700",
                    "transition-colors"
                  )}
                >
                  <Navigation className="w-4 h-4" />
                  Direções
                </button>
              )}
            </div>
          </div>

          <Popover.Arrow className="fill-white dark:fill-zinc-900" />
        </Popover.Content>
      </Popover.Portal>
    </Popover.Root>
  );
}
