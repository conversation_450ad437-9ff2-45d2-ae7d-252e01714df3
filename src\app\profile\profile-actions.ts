'use server';

import { createClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';
import { createSocialNotification } from '@/lib/actions/notifications';

export interface ProfileActionState {
  success: boolean;
  message: string;
  data?: any;
}

// Server action para seguir um usuário
export async function followUserAction(formData: FormData): Promise<ProfileActionState> {
  try {
    const targetUserId = formData.get('targetUserId') as string;
    
    if (!targetUserId) {
      return { success: false, message: 'ID do usuário é obrigatório' };
    }

    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, message: 'Usuário não autenticado' };
    }

    if (user.id === targetUserId) {
      return { success: false, message: 'Você não pode seguir a si mesmo' };
    }

    // Check if already following
    const { data: existingFollow } = await supabase
      .from('user_follows')
      .select('id')
      .eq('follower_id', user.id)
      .eq('following_id', targetUserId)
      .single();

    if (existingFollow) {
      return { success: false, message: 'Você já segue este usuário' };
    }

    // Create follow relationship
    const { error: followError } = await supabase
      .from('user_follows')
      .insert({
        follower_id: user.id,
        following_id: targetUserId,
        created_at: new Date().toISOString()
      });

    if (followError) {
      console.error('Error creating follow:', followError);
      return { success: false, message: 'Erro ao seguir usuário' };
    }

    // Update follower counts
    await supabase.rpc('increment_follower_count', { target_user_id: targetUserId });
    await supabase.rpc('increment_following_count', { target_user_id: user.id });

    // Criar notificação para o usuário seguido
    await createSocialNotification(
      targetUserId,
      user.id,
      'follow'
    );

    // Revalidate profile pages
    revalidatePath('/feed');

    return { success: true, message: 'Usuário seguido com sucesso' };
  } catch (error) {
    console.error('Error in followUserAction:', error);
    return { success: false, message: 'Erro interno do servidor' };
  }
}

// Server action para deixar de seguir um usuário
export async function unfollowUserAction(formData: FormData): Promise<ProfileActionState> {
  try {
    const targetUserId = formData.get('targetUserId') as string;
    
    if (!targetUserId) {
      return { success: false, message: 'ID do usuário é obrigatório' };
    }

    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, message: 'Usuário não autenticado' };
    }

    // Remove follow relationship
    const { error: unfollowError } = await supabase
      .from('user_follows')
      .delete()
      .eq('follower_id', user.id)
      .eq('following_id', targetUserId);

    if (unfollowError) {
      console.error('Error removing follow:', unfollowError);
      return { success: false, message: 'Erro ao deixar de seguir usuário' };
    }

    // Update follower counts
    await supabase.rpc('decrement_follower_count', { target_user_id: targetUserId });
    await supabase.rpc('decrement_following_count', { target_user_id: user.id });

    // Revalidate profile pages
    revalidatePath('/feed');

    return { success: true, message: 'Você deixou de seguir este usuário' };
  } catch (error) {
    console.error('Error in unfollowUserAction:', error);
    return { success: false, message: 'Erro interno do servidor' };
  }
}

// Server action para atualizar perfil
export async function updateProfileAction(formData: FormData): Promise<ProfileActionState> {
  try {
    const displayName = formData.get('displayName') as string;
    const bio = formData.get('bio') as string;
    const location = formData.get('location') as string;
    const website = formData.get('website') as string;
    const isPrivate = formData.get('isPrivate') === 'true';
    const followsRequired = formData.get('followsRequired') === 'true';

    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, message: 'Usuário não autenticado' };
    }

    // Update user profile
    const { error: updateError } = await supabase
      .from('user_profiles')
      .update({
        display_name: displayName?.trim() || null,
        bio: bio?.trim() || null,
        location: location?.trim() || null,
        website: website?.trim() || null,
        is_private: isPrivate,
        follows_required: followsRequired,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', user.id);

    if (updateError) {
      console.error('Error updating profile:', updateError);
      return { success: false, message: 'Erro ao atualizar perfil' };
    }

    // Revalidate profile pages
    revalidatePath('/feed');

    return { success: true, message: 'Perfil atualizado com sucesso' };
  } catch (error) {
    console.error('Error in updateProfileAction:', error);
    return { success: false, message: 'Erro interno do servidor' };
  }
} 