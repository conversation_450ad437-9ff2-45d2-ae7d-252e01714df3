/**
 * Location Utilities - Helper functions for location processing
 */

import type { Coordinates } from '../coordinates';

// Função auxiliar para mapear tipos para categorias do banco
export function mapTypeToCategory(locationType: string): string {
  const typeMap: Record<string, string> = {
    'restaurant': 'restaurant',
    'museum': 'museum',
    'park': 'park',
    'shopping': 'shopping',
    'hotel': 'hotel',
    'attraction': 'landmark',
    'landmark': 'landmark',
    'theater': 'entertainment',
    'entertainment': 'entertainment',
    'religious': 'religious',
    'beach': 'beach'
  };
  
  return typeMap[locationType.toLowerCase()] || 'landmark';
}

// Função auxiliar para normalizar nomes (melhorada para comparação de estabelecimentos)
export function normalizeLocationName(name: string): string {
  return name
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove acentos
    .replace(/[^\w\s]/g, '') // Remove pontuação
    .replace(/\b(restaurante|hotel|bar|lanchonete|cassino|shopping|centro|mercado|loja)\b/g, '') // Remove palavras comuns de estabelecimentos
    .replace(/\s+/g, ' ') // Normaliza espaços
    .trim();
}

// Função auxiliar para calcular similaridade
export function calculateSimilarity(str1: string, str2: string): number {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;
  
  if (longer.length === 0) return 1.0;
  
  const editDistance = levenshteinDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
}

// Função auxiliar para distância Levenshtein
export function levenshteinDistance(str1: string, str2: string): number {
  const matrix: number[][] = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(0));
  
  for (let i = 0; i <= str1.length; i++) matrix[0]![i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j]![0] = j;
  
  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j]![i] = Math.min(
        matrix[j]![i - 1]! + 1,
        matrix[j - 1]![i]! + 1,
        matrix[j - 1]![i - 1]! + indicator
      );
    }
  }
  
  return matrix[str2.length]![str1.length]!;
}

// Função auxiliar para gerar URLs de fotos
export function generatePhotoUrl(locationName: string, city: string): string {
  // Usar uma imagem padrão do Brasil para evitar URLs malformadas
  return `https://images.unsplash.com/photo-1483729558449-99ef09a8c325?w=800&h=600&fit=crop&crop=center`;
}

// Função auxiliar para calcular score de popularidade
export function calculatePopularityScore(locationType: string, city: string, countryCode: string): number {
  // Scores base por tipo de local
  const typeScores: Record<string, number> = {
    'restaurant': 70,
    'landmark': 85,
    'museum': 75,
    'park': 65,
    'shopping': 60,
    'hotel': 55,
    'entertainment': 70,
    'religious': 60,
    'beach': 80,
    'airport': 50,
    'station': 45
  };
  
  // Bonus por cidade (cidades maiores = maior popularidade base)
  const cityBonuses: Record<string, number> = {
    // Brasil
    'são paulo': 15,
    'rio de janeiro': 12,
    'salvador': 8,
    'brasília': 7,
    'fortaleza': 6,
    'belo horizonte': 5,
    
    // Internacional - Cidades principais
    'paris': 18,
    'london': 17,
    'new york': 20,
    'tokyo': 16,
    'roma': 14,
    'barcelona': 12,
    'madrid': 11,
    'berlin': 10,
    'amsterdam': 9,
    'sydney': 8
  };
  
  // Bonus por país (países desenvolvidos têm score maior)
  const countryBonuses: Record<string, number> = {
    'US': 10, 'FR': 8, 'GB': 8, 'DE': 7, 'IT': 7,
    'ES': 6, 'JP': 9, 'AU': 6, 'CA': 7, 'BR': 5,
    'AR': 4, 'PE': 3, 'CL': 4, 'CO': 3
  };
  
  const baseScore = typeScores[locationType.toLowerCase()] || 50;
  const cityBonus = cityBonuses[city.toLowerCase()] || 0;
  const countryBonus = countryBonuses[countryCode] || 0;
  
  return Math.min(100, baseScore + cityBonus + countryBonus);
}

// Função auxiliar para estimar faixa de preço
export function estimatePriceRange(locationType: string): string {
  const priceRanges: Record<string, string> = {
    'restaurant': 'moderate',
    'landmark': 'free',
    'museum': 'budget',
    'park': 'free',
    'shopping': 'varied',
    'hotel': 'moderate',
    'entertainment': 'moderate',
    'religious': 'free',
    'beach': 'free',
    'airport': 'free',
    'station': 'budget'
  };
  
  return priceRanges[locationType.toLowerCase()] || 'moderate';
}

// Função auxiliar para gerar descrição automática
export function generateLocationDescription(
  _locationName: string, // Marcado como não usado com underscore
  locationType: string, 
  city: string,
  countryName: string
): string {
  const templates: Record<string, string> = {
    'restaurant': `Restaurante localizado em ${city}, ${countryName}, descoberto através de busca inteligente.`,
    'landmark': `Ponto turístico importante em ${city}, ${countryName}, adicionado automaticamente ao banco.`,
    'museum': `Museu ou centro cultural em ${city}, ${countryName}, catalogado pelo sistema inteligente.`,
    'park': `Parque ou área verde em ${city}, ${countryName}, identificado automaticamente.`,
    'shopping': `Centro comercial ou loja em ${city}, ${countryName}, adicionado via descoberta automática.`,
    'hotel': `Hotel ou hospedagem em ${city}, ${countryName}, catalogado pelo sistema.`,
    'entertainment': `Local de entretenimento em ${city}, ${countryName}, descoberto automaticamente.`,
    'religious': `Local religioso ou cultural em ${city}, ${countryName}, adicionado ao banco de dados.`,
    'beach': `Praia ou área costeira próxima a ${city}, ${countryName}, catalogada automaticamente.`,
    'airport': `Aeroporto ou terminal de transporte em ${city}, ${countryName}.`,
    'station': `Estação de transporte público em ${city}, ${countryName}.`
  };
  
  return templates[locationType.toLowerCase()] || 
         `Local de interesse em ${city}, ${countryName}, adicionado automaticamente ao banco de dados do Voyagr.`;
}

// Função para determinar se dois estabelecimentos podem coexistir no mesmo local
export function canCoexistInSameLocation(name1: string, type1: string, name2: string, type2: string): boolean {
  const lower1 = name1.toLowerCase();
  const lower2 = name2.toLowerCase();
  
  // Casos onde estabelecimentos PODEM coexistir no mesmo local:
  
  // 1. Diferentes tipos de estabelecimento
  const differentTypes = type1 !== type2;
  
  // 2. Restaurantes/bares em hotéis
  const restaurantInHotel = (
    (lower1.includes('hotel') && (type2 === 'restaurant' || type2 === 'bar')) ||
    (lower2.includes('hotel') && (type1 === 'restaurant' || type1 === 'bar'))
  );
  
  // 3. Múltiplos estabelecimentos em shopping/centro comercial
  const inShoppingCenter = (
    lower1.includes('shopping') || lower2.includes('shopping') ||
    lower1.includes('centro') || lower2.includes('centro') ||
    lower1.includes('mall') || lower2.includes('mall')
  );
  
  // 4. Diferentes andares ou seções (indicado por números/letras)
  const differentFloors = (
    /\d+[°ºª]?\s*(andar|piso|floor)/i.test(name1) ||
    /\d+[°ºª]?\s*(andar|piso|floor)/i.test(name2) ||
    /loja\s*\d+/i.test(name1) || /loja\s*\d+/i.test(name2)
  );
  
  // 5. Cassinos em hotéis
  const casinoInHotel = (
    (lower1.includes('hotel') && lower2.includes('cassino')) ||
    (lower2.includes('hotel') && lower1.includes('cassino'))
  );
  
  // 6. Praças de alimentação
  const foodCourt = (
    lower1.includes('praça') || lower2.includes('praça') ||
    lower1.includes('food court') || lower2.includes('food court')
  );
  
  return differentTypes || restaurantInHotel || inShoppingCenter || 
         differentFloors || casinoInHotel || foodCourt;
}

// Busca fuzzy no database local
export function findFuzzyMatch(locationName: string, city: string, database: Map<string, Coordinates>): Coordinates | null {
  const searchTerms = locationName.toLowerCase().split(' ');
  const targetCity = city.toLowerCase();
  
  for (const [key, coords] of database.entries()) {
    const parts = key.split('|');
    if (parts.length !== 3) continue;
    
    const [name, , keyCity] = parts;
    if (!name || !keyCity) continue;
    
    if (keyCity !== targetCity) continue;
    
    // Verificar se pelo menos 50% das palavras coincidem
    const nameWords = name.split(' ');
    const matches = searchTerms.filter(term => 
      nameWords.some(word => word.includes(term) || term.includes(word))
    );
    
    if (matches.length >= Math.ceil(searchTerms.length * 0.5)) {
      return coords;
    }
  }
  
  return null;
} 