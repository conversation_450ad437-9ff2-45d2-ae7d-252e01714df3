-- =====================================================
-- SOCIAL ENHANCEMENTS - NOTIFICAÇÕES E REAÇÕES
-- =====================================================

-- Criar enum para tipos de notificação social
CREATE TYPE social_notification_type AS ENUM (
  'like',
  'comment', 
  'follow',
  'mention',
  'reply',
  'story_view'
);

-- Criar enum para tipos de reação
CREATE TYPE reaction_type AS ENUM (
  'like',
  'love',
  'laugh',
  'wow',
  'sad',
  'angry'
);

-- =====================================================
-- TABELA DE NOTIFICAÇÕES SOCIAIS
-- =====================================================
CREATE TABLE IF NOT EXISTS social_notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  actor_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  type social_notification_type NOT NULL,
  post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
  comment_id UUID REFERENCES post_comments(id) ON DELETE CASCADE,
  story_id UUID REFERENCES stories(id) ON DELETE CASCADE,
  content TEXT,
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_social_notifications_user_id ON social_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_social_notifications_actor_id ON social_notifications(actor_id);
CREATE INDEX IF NOT EXISTS idx_social_notifications_type ON social_notifications(type);
CREATE INDEX IF NOT EXISTS idx_social_notifications_created_at ON social_notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_social_notifications_is_read ON social_notifications(is_read);

-- =====================================================
-- TABELA DE REAÇÕES DE POSTS
-- =====================================================
CREATE TABLE IF NOT EXISTS post_reactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  reaction_type reaction_type NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Garantir que um usuário só pode ter uma reação por post
  UNIQUE(post_id, user_id)
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_post_reactions_post_id ON post_reactions(post_id);
CREATE INDEX IF NOT EXISTS idx_post_reactions_user_id ON post_reactions(user_id);
CREATE INDEX IF NOT EXISTS idx_post_reactions_type ON post_reactions(reaction_type);

-- =====================================================
-- RLS (ROW LEVEL SECURITY) POLICIES
-- =====================================================

-- Habilitar RLS nas tabelas
ALTER TABLE social_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_reactions ENABLE ROW LEVEL SECURITY;

-- Políticas para social_notifications
CREATE POLICY "Users can view their own notifications" ON social_notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON social_notifications
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can insert notifications" ON social_notifications
  FOR INSERT WITH CHECK (true);

-- Políticas para post_reactions
CREATE POLICY "Anyone can view post reactions" ON post_reactions
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own reactions" ON post_reactions
  FOR ALL USING (auth.uid() = user_id);

-- =====================================================
-- FUNÇÕES AUXILIARES
-- =====================================================

-- Função para contar notificações não lidas
CREATE OR REPLACE FUNCTION get_unread_notifications_count(target_user_id UUID)
RETURNS INTEGER AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)::INTEGER
    FROM social_notifications
    WHERE user_id = target_user_id AND is_read = FALSE
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para buscar resumo de reações de um post
CREATE OR REPLACE FUNCTION get_post_reaction_summary(target_post_id UUID)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_object_agg(
    reaction_type,
    json_build_object(
      'count', count,
      'users', users
    )
  ) INTO result
  FROM (
    SELECT 
      reaction_type,
      COUNT(*) as count,
      json_agg(
        json_build_object(
          'id', u.id,
          'username', u.username,
          'display_name', u.display_name,
          'avatar_url', u.avatar_url
        )
      ) as users
    FROM post_reactions pr
    JOIN users u ON pr.user_id = u.id
    WHERE pr.post_id = target_post_id
    GROUP BY reaction_type
  ) grouped;
  
  RETURN COALESCE(result, '{}'::json);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- TRIGGERS PARA ATUALIZAÇÃO AUTOMÁTICA
-- =====================================================

-- Trigger para atualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar trigger nas tabelas
CREATE TRIGGER update_social_notifications_updated_at
  BEFORE UPDATE ON social_notifications
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_post_reactions_updated_at
  BEFORE UPDATE ON post_reactions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- GRANTS DE PERMISSÃO
-- =====================================================

-- Permitir acesso às funções
GRANT EXECUTE ON FUNCTION get_unread_notifications_count(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_post_reaction_summary(UUID) TO authenticated;

-- =====================================================
-- DADOS INICIAIS (OPCIONAL)
-- =====================================================

-- Comentário: As tabelas estão prontas para uso.
-- As notificações serão criadas automaticamente pelas server actions.
-- As reações substituirão gradualmente o sistema de likes simples.
