// ========================================
// SISTEMA DE MODO VIAGEM - TIPOS TYPESCRIPT
// Funcionalidades revolucionárias para viajantes ativos
// ========================================



// ========================================
// TIPOS BÁSICOS
// ========================================

export interface TravelModeLocation {
  lat: number;
  lng: number;
  address?: string;
  timestamp?: string;
}

export interface WeatherData {
  temperature: number;
  condition: string;
  description: string;
  humidity: number;
  windSpeed: number;
  precipitation: number;
  uvIndex: number;
  icon: string;
  alerts?: WeatherAlert[];
  updated_at: string;
}

export interface WeatherAlert {
  type: 'rain' | 'storm' | 'extreme_heat' | 'strong_wind';
  severity: 'low' | 'medium' | 'high' | 'extreme';
  message: string;
  expires_at: string;
}

export interface RouteData {
  distance: number;
  duration: number;
  transport_mode: 'walking' | 'driving' | 'transit' | 'cycling';
  instructions: RouteInstruction[];
  traffic_delay?: number;
  updated_at: string;
}

export interface RouteInstruction {
  instruction: string;
  distance: number;
  duration: number;
  maneuver: string;
}

export interface AlternativeActivity {
  id: string;
  title: string;
  description: string;
  location_name: string;
  activity_type: string;
  reason: string; // Por que foi sugerida
  weather_dependent: boolean;
  cost_estimate?: number;
  rating?: number;
  photo_url?: string;
}

// ========================================
// LOGS E NOTIFICAÇÕES
// ========================================

export type TravelModeLogType = 
  | 'activity_checkin' 
  | 'expense_added' 
  | 'photo_upload' 
  | 'weather_alert' 
  | 'route_update'
  | 'mode_activated'
  | 'location_update'
  | 'suggestion_generated';

export interface TravelModeLog {
  id: string;
  itinerary_id: string;
  user_id: string;
  log_type: TravelModeLogType;
  activity_id?: string;
  content: Record<string, any>;
  location_lat?: number;
  location_lng?: number;
  created_at: string;
  metadata?: Record<string, any>;
}

export type NotificationType = 
  | 'weather_alert' 
  | 'activity_reminder' 
  | 'expense_split' 
  | 'route_update' 
  | 'suggestion'
  | 'emergency'
  | 'diary_reminder';

export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface TravelModeNotification {
  id: string;
  itinerary_id: string;
  user_id: string;
  notification_type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, any>;
  priority: NotificationPriority;
  is_read: boolean;
  expires_at?: string;
  created_at: string;
}

// ========================================
// DADOS EM TEMPO REAL
// ========================================

export interface TravelModeLiveData {
  id: string;
  itinerary_id: string;
  user_id: string;
  current_day: number;
  current_activity_id?: string;
  next_activity_id?: string;
  current_location?: TravelModeLocation;
  weather_data?: WeatherData;
  route_data?: RouteData;
  alternative_activities: AlternativeActivity[];
  updated_at: string;
  created_at: string;
}

// ========================================
// TRACKING DE ATIVIDADES
// ========================================

export type ActivityStatus = 'pending' | 'in_progress' | 'completed' | 'skipped';

export interface ActivityTracking {
  id: string;
  itinerary_id: string;
  activity_id: string;
  user_id: string;
  status: ActivityStatus;
  started_at?: string;
  completed_at?: string;
  notes?: string;
  location_lat?: number;
  location_lng?: number;
  created_at: string;
  updated_at: string;
}

export interface TravelModeDashboard {
  success: boolean;
  current_day: number;
  current_activity?: {
    id: string;
    title: string;
    description?: string;
    location_name?: string;
    start_time?: string;
    end_time?: string;
    day_number: number;
    status?: ActivityStatus;
    started_at?: string;
    completed_at?: string;
    notes?: string;
  };
  next_activity?: {
    id: string;
    title: string;
    description?: string;
    location_name?: string;
    start_time?: string;
    end_time?: string;
    day_number: number;
    status?: ActivityStatus;
  };
  weather?: WeatherData;
  notifications: TravelModeNotification[];
  recent_logs: TravelModeLog[];
  expense_summary: TravelModeExpenseStats;
  day_progress: {
    total_activities: number;
    completed_activities: number;
    current_day: number;
    completion_percentage?: number;
  };
  alternative_activities: AlternativeActivity[];
  expenses?: TravelModeExpense[];
  expense_categories?: TravelModeExpenseCategory[];
  expense_splits?: TravelModeExpenseSplit[];
  diary_entries?: DiaryEntry[];
  current_location?: TravelModeLocation;
  last_updated: string;
}

// ========================================
// SISTEMA DE DESPESAS
// ========================================

export interface TravelModeExpense {
  id: string;
  title: string;
  amount: number;
  currency: string;
  category: string;
  paid_by: string;
  location?: string | null;
  receipt_url?: string | null;
  is_split: boolean;
  split_method: 'equal' | 'custom';
  participants_count: number;
  notes?: string | null;
  status: 'pending' | 'confirmed' | 'disputed';
  created_at: string;
}

export interface TravelModeExpenseCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
  budget: number;
}

export interface TravelModeExpenseSplit {
  id: string;
  expense_id: string;
  participant_id: string;
  amount: number;
  currency: string;
  is_paid: boolean;
  created_at: string;
}

export interface TravelModeExpenseStats {
  total_spent: number;
  today_spent: number;
  today_planned: number;
  daily_average: number;
  remaining_budget: number;
  currency: string;
  expense_count: number;
  split_count: number;
  pending_settlements: number;
  category_breakdown: Record<string, number>;
  trend: 'up' | 'down' | 'stable';
  trend_percentage: number;
}

// ========================================
// DIVISÃO DE DESPESAS (LEGACY)
// ========================================

export interface ExpenseSplit {
  id: string;
  expense_id: string;
  itinerary_id: string;
  participant_id: string;
  amount: number;
  currency: string;
  is_paid: boolean;
  payment_method?: string;
  created_at: string;
  updated_at: string;
}

export interface ExpenseSplitRequest {
  expense_id: string;
  splits: {
    participant_id: string;
    amount: number;
  }[];
}

export interface ExpenseSummary {
  total_amount: number;
  my_share: number;
  paid_amount: number;
  pending_amount: number;
  splits: ExpenseSplit[];
  participants: {
    id: string;
    name: string;
    email: string;
    amount: number;
    is_paid: boolean;
  }[];
}

// ========================================
// DIÁRIO DE VIAGEM
// ========================================

export type TravelMood = 'happy' | 'excited' | 'relaxed' | 'tired' | 'adventurous' | 'nostalgic';

export interface DiaryPhoto {
  url: string;
  caption?: string;
  location?: TravelModeLocation;
  timestamp: string;
  size?: number;
  type?: string;
}

export interface DiaryEntry {
  id: string;
  itinerary_id: string;
  user_id: string;
  day_number: number;
  activity_id?: string;
  title?: string;
  content?: string;
  photos: DiaryPhoto[];
  mood?: TravelMood;
  weather?: string;
  location?: TravelModeLocation;
  created_at: string;
  updated_at: string;
}

export interface DiaryEntryCreate {
  day_number: number;
  activity_id?: string;
  title?: string | undefined;
  content?: string | undefined;
  photos?: DiaryPhoto[];
  mood?: TravelMood;
  weather?: string | undefined;
  location?: TravelModeLocation | undefined;
}

// ========================================
// CONFIGURAÇÕES
// ========================================

export interface TravelModeSettings {
  id: string;
  user_id: string;
  itinerary_id: string;
  notifications_enabled: boolean;
  location_sharing: boolean;
  weather_alerts: boolean;
  expense_auto_split: boolean;
  offline_mode: boolean;
  settings: {
    language?: string;
    currency?: string;
    distance_unit?: 'km' | 'miles';
    temperature_unit?: 'celsius' | 'fahrenheit';
    notification_sound?: boolean;
    auto_checkin?: boolean;
    photo_auto_backup?: boolean;
  };
  created_at: string;
  updated_at: string;
}

// ========================================
// RESPOSTAS DA API
// ========================================

export interface TravelModeActivationResponse {
  success: boolean;
  travel_mode_activated: boolean;
  current_day: number;
  current_activity_id?: string;
  next_activity_id?: string;
  itinerary_title: string;
  total_days: number;
  error?: string;
}

export interface TravelModeStatusResponse {
  is_active: boolean;
  can_activate: boolean;
  days_until_start: number | null;
  days_since_start: number | null;
  reason: string | null;
  itinerary_title?: string;
  start_date?: string;
  end_date?: string;
  participant_count?: number;
}

// ========================================
// HOOKS E CONTEXTO
// ========================================

export interface TravelModeContextType {
  isActive: boolean;
  dashboard?: TravelModeDashboard;
  settings?: TravelModeSettings;
  isLoading: boolean;
  error?: string;
  
  // Actions
  activateTravelMode: () => Promise<void>;
  deactivateTravelMode: () => Promise<void>;
  refreshDashboard: () => Promise<void>;
  updateLocation: (location: TravelModeLocation) => Promise<void>;
  addLog: (type: TravelModeLogType, content: Record<string, any>) => Promise<void>;
  markNotificationRead: (notificationId: string) => Promise<void>;
  createDiaryEntry: (entry: DiaryEntryCreate) => Promise<void>;
  updateSettings: (settings: Partial<TravelModeSettings['settings']>) => Promise<void>;
}

// ========================================
// FILTROS E PARÂMETROS
// ========================================

export interface TravelModeFilters {
  log_type?: TravelModeLogType[];
  notification_type?: NotificationType[];
  date_range?: {
    start: string;
    end: string;
  };
  priority?: NotificationPriority[];
  is_read?: boolean;
}

export interface TravelModeSearchParams {
  query?: string;
  filters?: TravelModeFilters;
  sort?: 'created_at' | 'priority' | 'type';
  order?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

// ========================================
// UTILS E HELPERS
// ========================================

export interface WeatherConditionMap {
  condition: string;
  icon: string;
  color: string;
  description: string;
  recommendations?: string[];
}

export interface ActivityTypeConfig {
  type: string;
  label: string;
  icon: string;
  color: string;
  weather_dependent: boolean;
  indoor_alternative?: string[];
}

export interface EmergencyContact {
  name: string;
  phone: string;
  relationship: string;
  is_local: boolean;
}

export interface TravelModePreferences {
  transport_mode: 'walking' | 'driving' | 'transit' | 'cycling';
  activity_pace: 'slow' | 'moderate' | 'fast';
  budget_level: 'budget' | 'moderate' | 'luxury';
  dietary_restrictions?: string[];
  accessibility_needs?: string[];
  interests: string[];
} 