import { NextRequest, NextResponse } from 'next/server';
import { searchItinerariesWithCache } from '@/lib/cache/itinerary-cache';
import type { ItinerarySearchParams, TripType } from '@/types/travel';

export async function POST(request: NextRequest) {
  try {
    const params: ItinerarySearchParams = await request.json();
    
    const result = await searchItinerariesWithCache(params);
    
    return NextResponse.json({
      success: true,
      data: result.data,
      fromCache: result.fromCache,
      cacheKey: result.cacheKey
    });
  } catch (error) {
    console.error('Erro na API de busca de itinerários:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Converter query params para ItinerarySearchParams
    const tripTypesParam = searchParams.get('trip_types');
    const tripTypes = tripTypesParam ? tripTypesParam.split(',') as TripType[] : undefined;

    const params: ItinerarySearchParams = {};

    // Adicionar apenas campos que não são undefined
    const searchTerm = searchParams.get('search_term');
    if (searchTerm) params.search_term = searchTerm;

    const userId = searchParams.get('user_id');
    if (userId) params.user_id = userId;

    if (searchParams.get('public_only') === 'true') {
      params.public_only = true;
    }

    if (tripTypes) params.trip_types = tripTypes;

    const cities = searchParams.get('cities')?.split(',');
    if (cities) params.cities = cities;

    const countries = searchParams.get('countries')?.split(',');
    if (countries) params.countries = countries;

    const minDays = searchParams.get('min_days');
    if (minDays) params.min_days = parseInt(minDays);

    const maxDays = searchParams.get('max_days');
    if (maxDays) params.max_days = parseInt(maxDays);

    const minCost = searchParams.get('min_cost');
    if (minCost) params.min_cost = parseFloat(minCost);

    const maxCost = searchParams.get('max_cost');
    if (maxCost) params.max_cost = parseFloat(maxCost);

    const limit = searchParams.get('limit');
    if (limit) params.limit = parseInt(limit);

    const offset = searchParams.get('offset');
    if (offset) params.offset = parseInt(offset);
    
    const result = await searchItinerariesWithCache(params);
    
    return NextResponse.json({
      success: true,
      data: result.data,
      fromCache: result.fromCache,
      cacheKey: result.cacheKey
    });
  } catch (error) {
    console.error('Erro na API de busca de itinerários:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
