import { NextRequest, NextResponse } from 'next/server';
import { searchItinerariesWithCache } from '@/lib/cache/itinerary-cache';
import type { ItinerarySearchParams } from '@/types/travel';

export async function POST(request: NextRequest) {
  try {
    const params: ItinerarySearchParams = await request.json();
    
    const result = await searchItinerariesWithCache(params);
    
    return NextResponse.json({
      success: true,
      data: result.data,
      fromCache: result.fromCache,
      cacheKey: result.cacheKey
    });
  } catch (error) {
    console.error('Erro na API de busca de itinerários:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Converter query params para ItinerarySearchParams
    const tripTypesParam = searchParams.get('trip_types');
    const tripTypes = tripTypesParam ? tripTypesParam.split(',') as TripType[] : undefined;

    const params: ItinerarySearchParams = {
      search_term: searchParams.get('search_term') || undefined,
      user_id: searchParams.get('user_id') || undefined,
      public_only: searchParams.get('public_only') === 'true',
      trip_types: tripTypes,
      cities: searchParams.get('cities')?.split(',') || undefined,
      countries: searchParams.get('countries')?.split(',') || undefined,
      min_duration: searchParams.get('min_duration') ? parseInt(searchParams.get('min_duration')!) : undefined,
      max_duration: searchParams.get('max_duration') ? parseInt(searchParams.get('max_duration')!) : undefined,
      min_budget: searchParams.get('min_budget') ? parseFloat(searchParams.get('min_budget')!) : undefined,
      max_budget: searchParams.get('max_budget') ? parseFloat(searchParams.get('max_budget')!) : undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : undefined,
    };
    
    const result = await searchItinerariesWithCache(params);
    
    return NextResponse.json({
      success: true,
      data: result.data,
      fromCache: result.fromCache,
      cacheKey: result.cacheKey
    });
  } catch (error) {
    console.error('Erro na API de busca de itinerários:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
