import { Metadata } from 'next';
import { Suspense } from 'react';
import { checkTravelModeStatusAction, autoActivateTravelMode } from '@/actions/travel-mode/travel-mode-actions';
import TravelModeDashboard from '@/components/travel-mode/TravelModeDashboard';
import TravelModeActivation from '@/components/travel-mode/TravelModeActivation';
import { TravelModeErrorBoundary } from '@/components/travel-mode/TravelModeErrorBoundary';
import { notFound } from 'next/navigation';

interface TravelModePageProps {
  params: Promise<{ id: string }>;
}

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: 'Modo Viagem - Voyagr',
    description: 'Dashboard inteligente para sua viagem',
  };
}

// Componente para status do modo viagem com ativação automática
async function TravelModeStatus({ itineraryId }: { itineraryId: string }) {
  const statusResult = await checkTravelModeStatusAction(itineraryId);
  
  if (!statusResult.success) {
    notFound();
  }

  const status = statusResult.data;

  // Tentar ativação automática se possível
  if (status.can_activate && !status.is_active) {
    try {
      await autoActivateTravelMode(itineraryId);
      // Revalidar status após ativação automática
      const newStatusResult = await checkTravelModeStatusAction(itineraryId);
      if (newStatusResult.success) {
        const newStatus = newStatusResult.data;

        // Se foi ativado automaticamente, mostrar dashboard
        if (newStatus.is_active) {
          return <TravelModeDashboard itineraryId={itineraryId} />;
        }
      }
    } catch (error) {
      console.log('Ativação automática não foi possível:', error);
    }
  }

  // Se já está ativo, mostrar dashboard
  if (status.is_active) {
    return <TravelModeDashboard itineraryId={itineraryId} />;
  }

  // Senão, mostrar componente de ativação
  return (
    <TravelModeActivation
      itineraryId={itineraryId}
      itineraryTitle={status.itinerary_title}
      startDate={status.start_date}
      endDate={status.end_date}
      participantCount={status.participant_count}
      canActivate={status.can_activate}
      reason={status.reason || undefined}
      daysUntilStart={status.days_until_start || undefined}
    />
  );
}

export default async function TravelModePage({ params }: TravelModePageProps) {
  const { id: itineraryId } = await params;

  return (
    <TravelModeErrorBoundary>
      <div className="min-h-screen bg-zinc-50 dark:bg-zinc-950">
        <div className="container mx-auto px-4 py-8">
          <Suspense
            fallback={
              <div className="flex items-center justify-center min-h-[60vh]">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              </div>
            }
          >
            <TravelModeStatus itineraryId={itineraryId} />
          </Suspense>
        </div>
      </div>
    </TravelModeErrorBoundary>
  );
}