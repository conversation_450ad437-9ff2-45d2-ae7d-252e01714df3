'use server';

import { createClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';
import { createSocialNotification } from '@/lib/actions/notifications';

export interface SocialActionState {
  success: boolean;
  message: string;
  data?: any;
}

// Server action para curtir/descurtir um post
export async function toggleLikeAction(formData: FormData): Promise<SocialActionState> {
  try {
    const postId = formData.get('postId') as string;
    const supabase = await createClient();

    // Get current user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        success: false,
        message: 'Você precisa estar logado para curtir posts',
      };
    }

    // Check if user already liked this post
    const { data: existingLike, error: checkError } = await supabase
      .from('post_likes')
      .select('id')
      .eq('post_id', postId)
      .eq('user_id', user.id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError;
    }

    if (existingLike) {
      // Unlike the post
      const { error: deleteError } = await supabase
        .from('post_likes')
        .delete()
        .eq('post_id', postId)
        .eq('user_id', user.id);

      if (deleteError) throw deleteError;

      // Update post likes count
      const { error: updateError } = await supabase.rpc('decrement_post_likes', {
        post_id: postId,
      });

      if (updateError) throw updateError;

      revalidatePath('/feed');

      return {
        success: true,
        message: 'Post descurtido com sucesso!',
        data: { liked: false },
      };
    } else {
      // Like the post
      const { error: insertError } = await supabase.from('post_likes').insert([
        {
          post_id: postId,
          user_id: user.id,
        },
      ]);

      if (insertError) throw insertError;

      // Update post likes count
      const { error: updateError } = await supabase.rpc('increment_post_likes', {
        post_id: postId,
      });

      if (updateError) throw updateError;

      // Buscar dados do post para notificação
      const { data: post } = await supabase
        .from('posts')
        .select('user_id')
        .eq('id', postId)
        .single();

      // Criar notificação para o autor do post
      if (post && post.user_id !== user.id) {
        await createSocialNotification(
          post.user_id,
          user.id,
          'like',
          { postId }
        );
      }

      revalidatePath('/feed');

      return {
        success: true,
        message: 'Post curtido com sucesso!',
        data: { liked: true },
      };
    }
  } catch (error) {
    console.error('Error toggling like:', error);
    return {
      success: false,
      message: 'Erro ao curtir/descurtir o post',
    };
  }
}

// Server action para compartilhar um post
export async function sharePostAction(formData: FormData): Promise<SocialActionState> {
  try {
    const postId = formData.get('postId') as string;
    const shareType = formData.get('shareType') as string;
    const supabase = await createClient();

    // Get current user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        success: false,
        message: 'Você precisa estar logado para compartilhar posts',
      };
    }

    // Insert share record
    const { error: insertError } = await supabase.from('post_shares').insert([
      {
        post_id: postId,
        user_id: user.id,
        comment: shareType || null,
      },
    ]);

    if (insertError) throw insertError;

    // Update post shares count
    const { error: updateError } = await supabase.rpc('increment_post_shares', {
      post_id: postId,
    });

    if (updateError) throw updateError;

    revalidatePath('/feed');

    return {
      success: true,
      message: 'Post compartilhado com sucesso!',
      data: { postId, shareType },
    };
  } catch (error) {
    console.error('Error sharing post:', error);
    return {
      success: false,
      message: 'Erro ao compartilhar o post',
    };
  }
}

// Server action para adicionar comentário
export async function addCommentAction(formData: FormData): Promise<SocialActionState> {
  try {
    const postId = formData.get('postId') as string;
    const content = formData.get('content') as string;
    const parentId = (formData.get('parentId') as string) || null;
    const supabase = await createClient();

    if (!content?.trim()) {
      return {
        success: false,
        message: 'O comentário não pode estar vazio',
      };
    }

    // Get current user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        success: false,
        message: 'Você precisa estar logado para comentar',
      };
    }

    // Insert comment
    const { data: newComment, error: insertError } = await supabase
      .from('post_comments')
      .insert([
        {
          post_id: postId,
          user_id: user.id,
          content: content.trim(),
          parent_id: parentId,
        },
      ])
      .select()
      .single();

    if (insertError) throw insertError;

    // Update post comments count
    const { error: updateError } = await supabase.rpc('increment_post_comments', {
      post_id: postId,
    });

    if (updateError) throw updateError;

    // Buscar dados do post para notificação
    const { data: post } = await supabase
      .from('posts')
      .select('user_id')
      .eq('id', postId)
      .single();

    // Criar notificação para o autor do post (se não for reply)
    if (post && post.user_id !== user.id && !parentId) {
      await createSocialNotification(
        post.user_id,
        user.id,
        'comment',
        {
          postId,
          commentId: newComment.id,
          content: content.trim().substring(0, 100) // Primeiros 100 chars
        }
      );
    }

    // Se for reply, notificar o autor do comentário pai
    if (parentId) {
      const { data: parentComment } = await supabase
        .from('post_comments')
        .select('user_id')
        .eq('id', parentId)
        .single();

      if (parentComment && parentComment.user_id !== user.id) {
        await createSocialNotification(
          parentComment.user_id,
          user.id,
          'reply',
          {
            postId,
            commentId: newComment.id,
            content: content.trim().substring(0, 100)
          }
        );
      }
    }

    revalidatePath('/feed');

    return {
      success: true,
      message: 'Comentário adicionado com sucesso!',
      data: { comment: newComment },
    };
  } catch (error) {
    console.error('Error adding comment:', error);
    return {
      success: false,
      message: 'Erro ao adicionar comentário',
    };
  }
}

// Server action para salvar/dessalvar um post
export async function toggleSavePostAction(formData: FormData): Promise<SocialActionState> {
  try {
    const postId = formData.get('postId') as string;
    const supabase = await createClient();

    // Get current user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        success: false,
        message: 'Você precisa estar logado para salvar posts',
      };
    }

    // Check if user already saved this post
    const { data: existingSave, error: checkError } = await supabase
      .from('saved_posts')
      .select('id')
      .eq('post_id', postId)
      .eq('user_id', user.id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError;
    }

    if (existingSave) {
      // Unsave the post
      const { error: deleteError } = await supabase
        .from('saved_posts')
        .delete()
        .eq('post_id', postId)
        .eq('user_id', user.id);

      if (deleteError) throw deleteError;

      revalidatePath('/feed');

      return {
        success: true,
        message: 'Post removido dos salvos!',
        data: { saved: false },
      };
    } else {
      // Save the post
      const { error: insertError } = await supabase.from('saved_posts').insert([
        {
          post_id: postId,
          user_id: user.id,
        },
      ]);

      if (insertError) throw insertError;

      revalidatePath('/feed');

      return {
        success: true,
        message: 'Post salvo com sucesso!',
        data: { saved: true },
      };
    }
  } catch (error) {
    console.error('Error toggling save post:', error);
    return {
      success: false,
      message: 'Erro ao salvar/dessalvar o post',
    };
  }
}

// Server action para excluir um post
export async function deletePostAction(formData: FormData): Promise<SocialActionState> {
  try {
    const postId = formData.get('postId') as string;
    const supabase = await createClient();

    // Get current user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        success: false,
        message: 'Você precisa estar logado para excluir posts',
      };
    }

    // Verify if the post belongs to the user
    const { data: post, error: postError } = await supabase
      .from('posts')
      .select('user_id')
      .eq('id', postId)
      .single();

    if (postError) {
      throw postError;
    }

    if (post.user_id !== user.id) {
      return {
        success: false,
        message: 'Você só pode excluir seus próprios posts',
      };
    }

    // Delete the post (cascade will handle related records)
    const { error: deleteError } = await supabase
      .from('posts')
      .delete()
      .eq('id', postId)
      .eq('user_id', user.id); // Extra security check

    if (deleteError) throw deleteError;

    revalidatePath('/feed');

    return {
      success: true,
      message: 'Post excluído com sucesso!',
      data: { postId },
    };
  } catch (error) {
    console.error('Error deleting post:', error);
    return {
      success: false,
      message: 'Erro ao excluir o post',
    };
  }
}

// Server action para editar um post
export async function editPostAction(formData: FormData): Promise<SocialActionState> {
  try {
    const postId = formData.get('postId') as string;
    const content = formData.get('content') as string;
    const tags = formData.get('tags') as string;
    const location = formData.get('location') as string;
    const supabase = await createClient();

    if (!content?.trim()) {
      return {
        success: false,
        message: 'O conteúdo do post não pode estar vazio',
      };
    }

    // Get current user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        success: false,
        message: 'Você precisa estar logado para editar posts',
      };
    }

    // Verify if the post belongs to the user
    const { data: post, error: postError } = await supabase
      .from('posts')
      .select('user_id')
      .eq('id', postId)
      .single();

    if (postError) {
      throw postError;
    }

    if (post.user_id !== user.id) {
      return {
        success: false,
        message: 'Você só pode editar seus próprios posts',
      };
    }

    // Parse tags and location
    const parsedTags = tags ? tags.split(',').map(tag => tag.trim()).filter(Boolean) : [];
    const parsedLocation = location ? JSON.parse(location) : null;

    // Update the post
    const { data: updatedPost, error: updateError } = await supabase
      .from('posts')
      .update({
        content: content.trim(),
        tags: parsedTags,
        location: parsedLocation,
        updated_at: new Date().toISOString(),
      })
      .eq('id', postId)
      .eq('user_id', user.id) // Extra security check
      .select()
      .single();

    if (updateError) throw updateError;

    revalidatePath('/feed');

    return {
      success: true,
      message: 'Post editado com sucesso!',
      data: { post: updatedPost },
    };
  } catch (error) {
    console.error('Error editing post:', error);
    return {
      success: false,
      message: 'Erro ao editar o post',
    };
  }
}

// Server action para excluir um comentário
export async function deleteCommentAction(formData: FormData): Promise<SocialActionState> {
  try {
    const commentId = formData.get('commentId') as string;
    const postId = formData.get('postId') as string;
    const supabase = await createClient();

    // Get current user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        success: false,
        message: 'Você precisa estar logado para excluir comentários',
      };
    }

    // Verify if the comment belongs to the user
    const { data: comment, error: commentError } = await supabase
      .from('post_comments')
      .select('user_id')
      .eq('id', commentId)
      .single();

    if (commentError) {
      throw commentError;
    }

    if (comment.user_id !== user.id) {
      return {
        success: false,
        message: 'Você só pode excluir seus próprios comentários',
      };
    }

    // Delete the comment
    const { error: deleteError } = await supabase
      .from('post_comments')
      .delete()
      .eq('id', commentId)
      .eq('user_id', user.id); // Extra security check

    if (deleteError) throw deleteError;

    // Update post comments count
    const { error: updateError } = await supabase.rpc('decrement_post_comments', {
      post_id: postId,
    });

    if (updateError) console.warn('Warning: Could not update comments count:', updateError);

    revalidatePath('/feed');

    return {
      success: true,
      message: 'Comentário excluído com sucesso!',
      data: { commentId },
    };
  } catch (error) {
    console.error('Error deleting comment:', error);
    return {
      success: false,
      message: 'Erro ao excluir comentário',
    };
  }
}
