'use server';

import { createClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';
import { createSocialNotification } from './notifications';
import type { ReactionType, PostReactionSummary } from '@/types/social';

// =====================================================
// REACTION ACTIONS - SISTEMA AVANÇADO DE REAÇÕES
// =====================================================

interface ReactionActionResult {
  success: boolean;
  data?: any;
  error?: string;
}

/**
 * Adiciona ou atualiza reação em um post
 */
export async function addPostReaction(
  postId: string,
  reactionType: ReactionType
): Promise<ReactionActionResult> {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: 'Usuário não autenticado' };
    }

    // Verificar se já existe reação do usuário para este post
    const { data: existingReaction, error: checkError } = await supabase
      .from('post_reactions')
      .select('id, reaction_type')
      .eq('post_id', postId)
      .eq('user_id', user.id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Erro ao verificar reação existente:', checkError);
      return { success: false, error: 'Erro ao verificar reação' };
    }

    if (existingReaction) {
      // Se já existe e é a mesma reação, remover
      if (existingReaction.reaction_type === reactionType) {
        return await removePostReaction(postId);
      }
      
      // Se existe mas é diferente, atualizar
      const { error: updateError } = await supabase
        .from('post_reactions')
        .update({ reaction_type: reactionType })
        .eq('id', existingReaction.id);

      if (updateError) {
        console.error('Erro ao atualizar reação:', updateError);
        return { success: false, error: 'Erro ao atualizar reação' };
      }
    } else {
      // Criar nova reação
      const { error: insertError } = await supabase
        .from('post_reactions')
        .insert({
          post_id: postId,
          user_id: user.id,
          reaction_type: reactionType
        });

      if (insertError) {
        console.error('Erro ao criar reação:', insertError);
        return { success: false, error: 'Erro ao criar reação' };
      }

      // Buscar dados do post para notificação
      const { data: post } = await supabase
        .from('posts')
        .select('user_id')
        .eq('id', postId)
        .single();

      // Criar notificação para o autor do post
      if (post && post.user_id !== user.id) {
        await createSocialNotification(
          post.user_id,
          user.id,
          'like', // Usar 'like' como tipo genérico para reações
          { postId }
        );
      }
    }

    revalidatePath('/feed');
    return { success: true };

  } catch (error) {
    console.error('Erro na ação de reação:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

/**
 * Remove reação de um post
 */
export async function removePostReaction(postId: string): Promise<ReactionActionResult> {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: 'Usuário não autenticado' };
    }

    // Remover reação
    const { error: deleteError } = await supabase
      .from('post_reactions')
      .delete()
      .eq('post_id', postId)
      .eq('user_id', user.id);

    if (deleteError) {
      console.error('Erro ao remover reação:', deleteError);
      return { success: false, error: 'Erro ao remover reação' };
    }

    revalidatePath('/feed');
    return { success: true };

  } catch (error) {
    console.error('Erro ao remover reação:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

/**
 * Busca resumo de reações de um post
 */
export async function getPostReactionSummary(postId: string): Promise<{
  success: boolean;
  data?: {
    reactions: PostReactionSummary;
    totalCount: number;
    userReaction?: ReactionType;
  };
  error?: string;
}> {
  try {
    const supabase = await createClient();
    
    // Buscar todas as reações do post
    const { data: reactions, error: reactionsError } = await supabase
      .from('post_reactions')
      .select(`
        reaction_type,
        user_id,
        users:user_id (
          id,
          username,
          display_name,
          avatar_url
        )
      `)
      .eq('post_id', postId);

    if (reactionsError) {
      console.error('Erro ao buscar reações:', reactionsError);
      return { success: false, error: 'Erro ao carregar reações' };
    }

    // Verificar reação do usuário atual
    let userReaction: ReactionType | undefined;
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const userReactionData = reactions?.find(r => r.user_id === user.id);
        userReaction = userReactionData?.reaction_type as ReactionType;
      }
    } catch {
      // Usuário não autenticado, continuar sem reação do usuário
    }

    // Agrupar reações por tipo
    const reactionSummary: PostReactionSummary = {
      like: { count: 0, users: [] },
      love: { count: 0, users: [] },
      laugh: { count: 0, users: [] },
      wow: { count: 0, users: [] },
      sad: { count: 0, users: [] },
      angry: { count: 0, users: [] }
    };

    let totalCount = 0;

    reactions?.forEach((reaction: any) => {
      const type = reaction.reaction_type as ReactionType;
      if (reactionSummary[type]) {
        reactionSummary[type].count++;
        reactionSummary[type].users.push(reaction.users);
        totalCount++;
      }
    });

    return {
      success: true,
      data: {
        reactions: reactionSummary,
        totalCount,
        userReaction
      }
    };

  } catch (error) {
    console.error('Erro ao buscar resumo de reações:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

/**
 * Busca usuários que reagiram a um post
 */
export async function getPostReactionUsers(
  postId: string,
  reactionType?: ReactionType
): Promise<{
  success: boolean;
  data?: Array<{
    user: any;
    reaction_type: ReactionType;
    created_at: string;
  }>;
  error?: string;
}> {
  try {
    const supabase = await createClient();
    
    let query = supabase
      .from('post_reactions')
      .select(`
        reaction_type,
        created_at,
        users:user_id (
          id,
          username,
          display_name,
          avatar_url,
          verified
        )
      `)
      .eq('post_id', postId)
      .order('created_at', { ascending: false });

    // Filtrar por tipo de reação se especificado
    if (reactionType) {
      query = query.eq('reaction_type', reactionType);
    }

    const { data: reactions, error } = await query;

    if (error) {
      console.error('Erro ao buscar usuários das reações:', error);
      return { success: false, error: 'Erro ao carregar usuários' };
    }

    const formattedData = reactions?.map((reaction: any) => ({
      user: reaction.users,
      reaction_type: reaction.reaction_type,
      created_at: reaction.created_at
    })) || [];

    return { success: true, data: formattedData };

  } catch (error) {
    console.error('Erro ao buscar usuários das reações:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}
