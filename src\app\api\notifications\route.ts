import { NextRequest, NextResponse } from 'next/server';
import { getUserNotifications, markNotificationAsRead, markAllNotificationsAsRead } from '@/lib/actions/notifications';

export async function GET() {
  try {
    const result = await getUserNotifications();
    return NextResponse.json(result);
  } catch (error) {
    console.error('Erro na API de notificações:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, notificationId } = body;

    if (action === 'markAsRead' && notificationId) {
      const result = await markNotificationAsRead(notificationId);
      return NextResponse.json(result);
    }

    if (action === 'markAllAsRead') {
      const result = await markAllNotificationsAsRead();
      return NextResponse.json(result);
    }

    return NextResponse.json(
      { success: false, error: 'Ação inválida' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Erro na API de notificações:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
