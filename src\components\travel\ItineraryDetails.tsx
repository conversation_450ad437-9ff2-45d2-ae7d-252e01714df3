'use client';

import { useState, useTransition } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  MapPin, Calendar, User, Heart, Bookmark, Eye, Share2, 
  Clock, CheckSquare, Edit
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { toggleItineraryLike, toggleItinerarySave } from '@/lib/actions/travel-actions';
import type { ItineraryWithDetails } from '@/types/travel';
import { TRIP_TYPE_LABELS, CREATION_METHOD_LABELS } from '@/types/travel';
import TravelModeActivator from '@/components/travel-mode/TravelModeActivator';

interface ItineraryDetailsProps {
  initialData: ItineraryWithDetails;
  currentUserId?: string | undefined;
}

export default function ItineraryDetails({ initialData, currentUserId }: ItineraryDetailsProps) {
  const [itinerary, setItinerary] = useState(initialData);
  const [isPending, startTransition] = useTransition();

  const isOwner = currentUserId && currentUserId === itinerary.user_id;

  const handleLike = () => {
    startTransition(async () => {
      const result = await toggleItineraryLike(itinerary.id);
      if (result.success && result.data) {
        setItinerary(prev => ({
          ...prev,
          likes_count: result.data.is_liked ? prev.likes_count + 1 : prev.likes_count - 1,
          user_interactions: {
            ...prev.user_interactions,
            is_liked: result.data.is_liked
          }
        }));
      }
    });
  };

  const handleSave = () => {
    startTransition(async () => {
      const result = await toggleItinerarySave(itinerary.id);
      if (result.success && result.data) {
        setItinerary(prev => ({
          ...prev,
          saves_count: result.data.is_saved ? prev.saves_count + 1 : prev.saves_count - 1,
          user_interactions: {
            ...prev.user_interactions,
            is_saved: result.data.is_saved
          }
        }));
      }
    });
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        const shareData: ShareData = {
          title: itinerary.title,
          url: window.location.href,
        };
        
        if (itinerary.description) {
          shareData.text = itinerary.description;
        }
        
        await navigator.share(shareData);
      } catch (err) {
        // Fallback para clipboard
        navigator.clipboard.writeText(window.location.href);
      }
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="space-y-6">
        {/* Cover Image */}
        {itinerary.cover_image_url && (
          <div className="relative aspect-video rounded-lg overflow-hidden">
            <Image
              src={itinerary.cover_image_url}
              alt={itinerary.title}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
              className="object-cover"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
            
            {/* Status Badge */}
            <div className="absolute top-4 left-4">
              <Badge variant={itinerary.status === 'public' ? 'default' : 'secondary'}>
                {itinerary.status === 'public' ? 'Público' : 'Privado'}
              </Badge>
            </div>
          </div>
        )}

        {/* Title and basic info */}
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="space-y-2 flex-1">
              <h1 className="text-3xl font-bold">{itinerary.title}</h1>
              {itinerary.description && (
                <p className="text-lg text-muted-foreground">{itinerary.description}</p>
              )}
            </div>
            
            {/* Action buttons */}
            <div className="flex items-center gap-2 ml-4">
              {isOwner && (
                <Button variant="default" size="sm" asChild>
                  <Link href={`/itineraries/${itinerary.id}/edit`}>
                    <Edit className="h-4 w-4 mr-1" />
                    Editar
                  </Link>
                </Button>
              )}
              
              <Button
                variant={itinerary.user_interactions.is_liked ? 'default' : 'outline'}
                size="sm"
                onClick={handleLike}
                disabled={isPending}
              >
                <Heart className={`h-4 w-4 mr-1 ${itinerary.user_interactions.is_liked ? 'fill-current' : ''}`} />
                {itinerary.likes_count}
              </Button>
              
              <Button
                variant={itinerary.user_interactions.is_saved ? 'default' : 'outline'}
                size="sm"
                onClick={handleSave}
                disabled={isPending}
              >
                <Bookmark className={`h-4 w-4 mr-1 ${itinerary.user_interactions.is_saved ? 'fill-current' : ''}`} />
                {itinerary.saves_count}
              </Button>
              
              <Button variant="outline" size="sm" onClick={handleShare}>
                <Share2 className="h-4 w-4 mr-1" />
                Compartilhar
              </Button>
            </div>
          </div>

          {/* Meta information */}
          <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <User className="h-4 w-4" />
              <Link 
                href={`/profile/${itinerary.author.username}`}
                className="hover:text-primary transition-colors"
              >
                {itinerary.author.display_name}
              </Link>
            </div>
            
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {itinerary.start_date && itinerary.end_date ? (
                <span>
                  {format(new Date(itinerary.start_date), 'dd/MM/yyyy', { locale: ptBR })} - {' '}
                  {format(new Date(itinerary.end_date), 'dd/MM/yyyy', { locale: ptBR })}
                </span>
              ) : (
                <span>{itinerary.total_days} dia{itinerary.total_days !== 1 ? 's' : ''}</span>
              )}
            </div>

            <div className="flex items-center gap-1">
              <Eye className="h-4 w-4" />
              <span>{itinerary.views_count} visualizações</span>
            </div>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-2">
            <Badge variant="outline">
              {TRIP_TYPE_LABELS[itinerary.trip_type]}
            </Badge>
            
            <Badge variant="outline">
              {CREATION_METHOD_LABELS[itinerary.creation_method]}
            </Badge>

            {itinerary.estimated_cost_min && itinerary.estimated_cost_max && (
              <Badge variant="outline">
                R$ {itinerary.estimated_cost_min} - R$ {itinerary.estimated_cost_max}
              </Badge>
            )}

            {itinerary.cities.map(city => (
              <Badge key={city} variant="secondary">
                <MapPin className="h-3 w-3 mr-1" />
                {city}
              </Badge>
            ))}
          </div>
        </div>
        
        {/* Travel Mode Activator */}
        <TravelModeActivator
          itineraryId={itinerary.id}
          startDate={itinerary.start_date}
          endDate={itinerary.end_date}
          isOwner={!!isOwner}
        />
      </div>

      <Separator />

      {/* Days and Activities */}
      <div className="space-y-6">
        <h2 className="text-2xl font-semibold flex items-center gap-2">
          <Calendar className="h-6 w-6" />
          Roteiro Detalhado
        </h2>

        {itinerary.days.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">
                Este roteiro ainda não possui dias planejados.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {itinerary.days
              .sort((a, b) => a.day_number - b.day_number)
              .map(day => {
                const dayActivities = (itinerary.activities || [])
                  .filter(activity => activity.day_id === day.id)
                  .sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));

                return (
                  <Card key={day.id}>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-bold">
                            {day.day_number}
                          </div>
                          <div>
                            <h3 className="text-lg">{day.title || `Dia ${day.day_number}`}</h3>
                            {day.date && (
                              <p className="text-sm text-muted-foreground">
                                {format(new Date(day.date), 'EEEE, dd/MM/yyyy', { locale: ptBR })}
                              </p>
                            )}
                          </div>
                        </div>

                        {day.location_city && (
                          <Badge variant="outline">
                            <MapPin className="h-3 w-3 mr-1" />
                            {day.location_city}
                          </Badge>
                        )}
                      </CardTitle>
                      
                      {day.description && (
                        <p className="text-muted-foreground">{day.description}</p>
                      )}
                    </CardHeader>

                    <CardContent>
                      {dayActivities.length === 0 ? (
                        <div className="text-center py-6 text-muted-foreground">
                          <Clock className="h-8 w-8 mx-auto mb-2" />
                          <p>Nenhuma atividade planejada para este dia</p>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {dayActivities.map((activity, index) => (
                            <div key={activity.id} className="flex gap-4">
                              <div className="flex flex-col items-center">
                                <div className="w-2 h-2 bg-primary rounded-full" />
                                {index < dayActivities.length - 1 && (
                                  <div className="w-px h-16 bg-border mt-2" />
                                )}
                              </div>
                              
                              <div className="flex-1 space-y-2">
                                <div className="flex items-start justify-between">
                                  <div>
                                    <h4 className="font-medium">{activity.title}</h4>
                                    {activity.description && (
                                      <p className="text-sm text-muted-foreground mt-1">
                                        {activity.description}
                                      </p>
                                    )}
                                  </div>
                                  
                                  {activity.cost_estimate && (
                                    <Badge variant="outline" className="ml-2">
                                      R$ {activity.cost_estimate}
                                    </Badge>
                                  )}
                                </div>

                                <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
                                  {activity.start_time && activity.end_time && (
                                    <span className="flex items-center gap-1">
                                      <Clock className="h-3 w-3" />
                                      {activity.start_time} - {activity.end_time}
                                    </span>
                                  )}
                                  
                                  {activity.location_name && (
                                    <span className="flex items-center gap-1">
                                      <MapPin className="h-3 w-3" />
                                      {activity.location_name}
                                    </span>
                                  )}

                                  {activity.activity_type && (
                                    <Badge variant="secondary" className="text-xs">
                                      {activity.activity_type}
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
          </div>
        )}
      </div>

      {/* Checklist */}
      {itinerary.checklist.length > 0 && (
        <>
          <Separator />
          <div className="space-y-4">
            <h2 className="text-2xl font-semibold flex items-center gap-2">
              <CheckSquare className="h-6 w-6" />
              Lista de Verificação
            </h2>

            <Card>
              <CardContent className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {itinerary.checklist
                    .sort((a, b) => a.sort_order - b.sort_order)
                    .map(item => (
                      <div key={item.id} className="flex items-center gap-2">
                        <CheckSquare className={`h-4 w-4 ${item.is_completed ? 'text-green-600' : 'text-muted-foreground'}`} />
                        <span className={item.is_completed ? 'line-through text-muted-foreground' : ''}>
                          {item.item_text}
                        </span>
                        {item.category && (
                          <Badge variant="outline" className="text-xs ml-auto">
                            {item.category}
                          </Badge>
                        )}
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </div>
  );
} 