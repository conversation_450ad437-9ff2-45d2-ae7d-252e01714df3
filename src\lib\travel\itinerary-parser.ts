/**
 * Itinerary Parser Module - Intelligent Response Processing
 * Handles parsing of Gemini AI generated itineraries into structured data
 */

import type { 
  AIGeneratedItinerary, 
  AIItineraryRequest
} from '@/types/travel';
import type { Coordinates } from './coordinates';
import { getMockCoordinatesForCity } from './coordinates';
import { GLOBAL_LOCATIONS_DB } from './coordinates-cache';

export async function parseGeminiItinerary(
  text: string, 
  request: AIItineraryRequest, 
  coordinatesMap: Map<string, Coordinates>
): Promise<AIGeneratedItinerary> {
  const lines = text.split('\n').map(line => line.trim()).filter(Boolean);
  
  // ✅ CORREÇÃO CRÍTICA: Extração correta do título e descrição
  let title = extractSection(lines, '**TÍTULO**') || extractSection(lines, 'TÍTULO');
  let description = extractSection(lines, '**DESCRIÇÃO**') || extractSection(lines, 'DESCRIÇÃO');
  
  // Fallback: buscar por padrões alternativos
  if (!title) {
    // Tentar encontrar linha que começa com "**" e parece ser um título
    for (const line of lines) {
      if (line.startsWith('**') && !line.includes('DESCRIÇÃO') && !line.includes('CUSTO') && !line.includes('CHECKLIST')) {
        const match = line.match(/\*\*([^*]+)\*\*/);
        if (match && match[1] && match[1].length > 10 && match[1].length < 100) {
          title = match[1].trim();
          break;
        }
      }
    }
  }
  
  // Se ainda não tem título, criar um baseado no request
  if (!title) {
    title = `Roteiro ${request.trip_type} - ${request.cities.join(' e ')}`;
  }
  
  // Se ainda não tem descrição, criar uma baseada no request
  if (!description) {
    description = `Descubra o melhor de ${request.cities.join(', ')} em ${request.total_days} dias inesquecíveis.`;
  }
  
  // ✅ VALIDAÇÃO: Garantir que título não é a descrição
  if (title.toLowerCase().includes('mergulhe') || title.toLowerCase().includes('descubra') || title.length > 150) {
    console.warn('⚠️ Título parece ser descrição, corrigindo...');
    title = `Roteiro ${request.trip_type} - ${request.cities.join(' e ')}`;
  }
  
  // ✅ LIMPEZA: Remover formatação markdown e limitações
  title = title.replace(/\*\*/g, '').replace(/\[|\]/g, '').trim();
  description = description.replace(/\*\*/g, '').replace(/\[|\]/g, '').trim();
  
  // ✅ LIMITE: Garantir que títulos não excedam limite de VARCHAR
  if (title.length > 200) {
    title = title.substring(0, 197) + '...';
  }
  
  console.log(`✅ Título corrigido: "${title}"`);
  console.log(`✅ Descrição: "${description.substring(0, 100)}..."`);
  
  const costInfo = extractCostInfo(lines);
  
  // Parse daily activities with optimized coordinate matching
  const activities = await parseDailyActivities(lines, coordinatesMap, request.total_days);

  // Extract checklist
  const checklist = extractChecklist(lines);

  return {
    title,
    description,
    cover_image_url: `https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=1600&h=900&fit=crop&crop=center`,
    estimated_cost_min: costInfo.min,
    estimated_cost_max: costInfo.max,
    days: organizeActivitiesByDays(activities, request.total_days),
    checklist
  };
}

async function parseDailyActivities(
  lines: string[], 
  coordinatesMap: Map<string, Coordinates>,
  totalDays: number
): Promise<Array<{
  day: number;
  time: string;
  location: string;
  description: string;
  estimated_cost: number;
  coordinates: { latitude: number; longitude: number } | null;
  address?: string;
  photo_url?: string;
}>> {
  const activities: Array<{
    day: number;
    time: string;
    location: string;
    description: string;
    estimated_cost: number;
    coordinates: { latitude: number; longitude: number } | null;
    address?: string;
    photo_url?: string;
  }> = [];
  let currentDay = 1;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    if (!line) continue;
    
    // Detect day headers
    if (line.match(/^DIA\s+\d+/i)) {
      const dayMatch = line.match(/DIA\s+(\d+)/i);
      if (dayMatch && dayMatch[1]) {
        const parsedDay = parseInt(dayMatch[1]);
        
        // Validar se o dia está dentro do limite solicitado
        if (parsedDay > totalDays) {
          console.warn(`⚠️ Dia ${parsedDay} excede totalDays ${totalDays}, limitando para dia ${totalDays}`);
          currentDay = totalDays;
        } else {
          currentDay = parsedDay;
        }
      }
      continue;
    }
    
    // Parse activity line with enhanced coordinate matching
    const activityMatch = line.match(/^•\s*(\d{1,2}):(\d{2})\s*-\s*(.+?)\s*-\s*(.+?)\s*-\s*R\$\s*(\d+(?:,\d+)?(?:\.\d{2})?)/);
    
    if (activityMatch) {
      const hours = activityMatch[1];
      const minutes = activityMatch[2];
      const locationName = activityMatch[3];
      const description = activityMatch[4];
      const cost = activityMatch[5];
      
      if (!hours || !minutes || !locationName || !description || !cost) continue;
      
      // Garantir que currentDay não exceda totalDays
      const validDay = Math.min(currentDay, totalDays);
      const time = `${hours.padStart(2, '0')}:${minutes}`;
      
      // Enhanced coordinate matching with multiple strategies
      const coordinates = await findOptimalCoordinatesWithFallback(locationName, coordinatesMap);
      
      if (coordinates) {
        activities.push({
          day: validDay,
          time,
          location: locationName.trim(),
          description: description.trim(),
          estimated_cost: parseFloat(cost.replace(',', '.')),
          coordinates: {
            latitude: coordinates.latitude,
            longitude: coordinates.longitude
          },
          address: coordinates.address,
          ...(coordinates.photo_url && { photo_url: coordinates.photo_url })
        });
        
        console.log(`✅ Atividade mapeada: ${locationName} [${coordinates.latitude}, ${coordinates.longitude}] - Dia ${validDay}`);
      } else {
        // Log para debug - atividade sem coordenadas (mesmo após fallbacks)
        console.warn(`⚠️ Atividade sem coordenadas (todos os fallbacks falharam): ${locationName}`);
        
        // Adicionar atividade mesmo sem coordenadas (com fallback)
        activities.push({
          day: validDay,
          time,
          location: locationName.trim(),
          description: description.trim(),
          estimated_cost: parseFloat(cost.replace(',', '.')),
          coordinates: null,
          address: `${locationName.trim()}, Brasil`
        });
      }
    }
  }
  
  return activities;
}

function organizeActivitiesByDays(
  activities: Array<{
    day: number;
    time: string;
    location: string;
    description: string;
    estimated_cost: number;
    coordinates: { latitude: number; longitude: number } | null;
    address?: string;
    photo_url?: string;
  }>,
  totalDays: number
) {
  const days = [];
  
  console.log(`🗓️ Organizando ${activities.length} atividades em ${totalDays} dias`);
  
  // Identificar quais dias têm atividades
  const daysWithActivities = new Set(activities.map(act => act.day));
  const maxDayFound = Math.max(...Array.from(daysWithActivities), 0);
  
  // 🚨 CORREÇÃO CRÍTICA: Sempre redistribuir se há desbalanceamento
  const needsRedistribution = maxDayFound > totalDays || checkIfNeedsRedistribution(activities, totalDays);
  
  if (needsRedistribution) {
    console.warn(`⚠️ Redistribuição necessária. Dias encontrados: ${maxDayFound}, Total solicitado: ${totalDays}`);
    activities = redistributeActivities(activities, totalDays);
    console.log(`✅ Atividades redistribuídas com sucesso`);
  }
  
  for (let dayNum = 1; dayNum <= totalDays; dayNum++) {
    const dayActivities = activities.filter(act => act.day === dayNum);
    
    console.log(`📅 Dia ${dayNum}: ${dayActivities.length} atividades`);
    
    // 🔧 MELHORAMENTO: Só gerar atividades padrão se realmente não houver nenhuma atividade
    if (dayActivities.length === 0) {
      console.log(`📝 Gerando atividades mínimas para dia ${dayNum} (sem atividades da AI)`);
      
             const defaultActivities = generateDefaultActivitiesForDay(dayNum, totalDays);
      
      days.push({
        day_number: dayNum,
        title: `Dia ${dayNum} - Explorações Livres`,
        description: `Dia para explorar livremente e descobrir novos locais`,
        activities: defaultActivities
      });
    } else {
      days.push({
        day_number: dayNum,
        title: `Dia ${dayNum}`,
        description: `Explorações do dia ${dayNum}`,
        activities: dayActivities.map(act => ({
          title: act.location,
          description: act.description,
          start_time: act.time,
          location_name: act.location,
          address: act.address || `${act.location}, Brasil`,
          latitude: act.coordinates?.latitude || 0,
          longitude: act.coordinates?.longitude || 0,
          activity_type: determineActivityType(act.location, act.description),
          cost_estimate: act.estimated_cost,
          ...(act.photo_url && { photo_url: act.photo_url })
        }))
      });
    }
  }

  return days;
}

// Nova função para redistribuir atividades quando excedem totalDays
function redistributeActivities(
  activities: Array<{
    day: number;
    time: string;
    location: string;
    description: string;
    estimated_cost: number;
    coordinates: { latitude: number; longitude: number } | null;
    address?: string;
    photo_url?: string;
  }>,
  totalDays: number
) {
  console.log(`🔄 Redistribuindo ${activities.length} atividades para ${totalDays} dias`);
  
  // Ordenar atividades por horário para manter ordem cronológica
  const sortedActivities = [...activities].sort((a, b) => {
    // Primeiro por dia original, depois por horário
    if (a.day !== b.day) return a.day - b.day;
    return a.time.localeCompare(b.time);
  });
  
  // Calcular distribuição equilibrada
  const activitiesPerDay = Math.floor(sortedActivities.length / totalDays);
  const remainingActivities = sortedActivities.length % totalDays;
  
  console.log(`📊 ${activitiesPerDay} atividades por dia, ${remainingActivities} extras`);
  
  const redistributed = [];
  let currentIndex = 0;
  
  for (let day = 1; day <= totalDays; day++) {
    // Alguns dias terão uma atividade extra
    const activitiesForThisDay = activitiesPerDay + (day <= remainingActivities ? 1 : 0);
    
    console.log(`📅 Dia ${day}: ${activitiesForThisDay} atividades`);
    
    // Distribuir horários ao longo do dia de forma inteligente
    const timeSlots = generateTimeSlots(activitiesForThisDay);
    
    for (let i = 0; i < activitiesForThisDay && currentIndex < sortedActivities.length; i++) {
      const activity = sortedActivities[currentIndex];
      if (activity) {
        redistributed.push({
          ...activity,
          day: day,
          time: timeSlots[i] || activity.time // Usar novo horário ou manter original
        });
        currentIndex++;
      }
    }
  }
  
  console.log(`✅ Redistribuição concluída: ${redistributed.length} atividades distribuídas`);
  
  // 🔍 VALIDAÇÃO FINAL: Verificar se distribuição está equilibrada
  const finalDistribution: Record<number, number> = {};
  redistributed.forEach(act => {
    finalDistribution[act.day] = (finalDistribution[act.day] || 0) + 1;
  });
  
  const counts = Object.values(finalDistribution);
  const maxCount = Math.max(...counts);
  const minCount = Math.min(...counts);
  const isBalanced = (maxCount - minCount) <= 1; // Diferença máxima de 1 atividade entre dias
  
  console.log(`🧮 Validação de distribuição:`, {
    distribuicao: finalDistribution,
    equilibrada: isBalanced,
    diferenca: maxCount - minCount
  });
  
  if (!isBalanced) {
    console.warn(`⚠️ DISTRIBUIÇÃO AINDA DESEQUILIBRADA após redistribuição!`);
  }
  
  return redistributed;
}

// Nova função para gerar horários inteligentes ao longo do dia
function generateTimeSlots(activityCount: number): string[] {
  if (activityCount === 0) return [];
  
  // Horários base para diferentes quantidades de atividades
  const timeSlotTemplates: Record<number, string[]> = {
    1: ['10:00'],
    2: ['09:30', '15:00'],
    3: ['09:00', '13:00', '17:00'],
    4: ['09:00', '12:00', '15:00', '18:30'],
    5: ['09:00', '11:30', '14:00', '16:30', '19:00'],
    6: ['09:00', '11:00', '13:00', '15:00', '17:00', '19:30'],
    7: ['08:30', '10:30', '12:30', '14:30', '16:30', '18:30', '20:00'],
    8: ['08:00', '10:00', '12:00', '14:00', '15:30', '17:00', '18:30', '20:00']
  };
  
  // Para mais de 8 atividades, distribuir uniformemente
  if (activityCount > 8) {
    const slots = [];
    const startHour = 8;
    const endHour = 20;
    const totalMinutes = (endHour - startHour) * 60;
    const interval = totalMinutes / (activityCount - 1);
    
    for (let i = 0; i < activityCount; i++) {
      const minutes = startHour * 60 + (i * interval);
      const hour = Math.floor(minutes / 60);
      const minute = Math.round(minutes % 60);
      slots.push(`${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`);
    }
    return slots;
  }
  
  return timeSlotTemplates[activityCount] || timeSlotTemplates[3] || ['09:00', '13:00', '17:00'];
}

// Função para verificar se as atividades precisam ser redistribuídas
function checkIfNeedsRedistribution(activities: Array<{ day: number }>, totalDays: number): boolean {
  // Calcular distribuição atual por dia
  const activitiesByDay: Record<number, number> = {};
  
  for (let day = 1; day <= totalDays; day++) {
    activitiesByDay[day] = 0;
  }
  
  activities.forEach(act => {
    if (act.day >= 1 && act.day <= totalDays) {
      activitiesByDay[act.day] = (activitiesByDay[act.day] || 0) + 1;
    }
  });
  
  // Verificar se há desbalanceamento significativo
  const counts = Object.values(activitiesByDay);
  const maxActivities = Math.max(...counts);
  const minActivities = Math.min(...counts);
  const threshold = Math.ceil(activities.length / totalDays);
  
  // Se a diferença entre dias é muito grande, redistribuir
  const isUnbalanced = (maxActivities - minActivities) > threshold;
  
  console.log(`🧮 Análise de distribuição: máx=${maxActivities}, mín=${minActivities}, threshold=${threshold}, desbalanceado=${isUnbalanced}`);
  
  return isUnbalanced;
}

// Nova função para gerar atividades padrão para dias vazios
function generateDefaultActivitiesForDay(dayNumber: number, totalDays: number) {
  const isLastDay = dayNumber === totalDays;
  const isFirstDay = dayNumber === 1;
  
  return [
    {
      title: isFirstDay ? 'Check-in e orientação' : 'Café da manhã regional',
      description: isFirstDay ? 
        'Chegada, check-in no hotel e primeira orientação sobre a cidade' :
        'Comece o dia com um café da manhã típico da região',
      start_time: '09:00',
      location_name: isFirstDay ? 'Hotel/Hospedagem' : 'Café Local',
      address: 'Centro da cidade, Brasil',
      latitude: 0,
      longitude: 0,
      activity_type: isFirstDay ? 'hotel' : 'restaurant',
      cost_estimate: isFirstDay ? 0 : 25
    },
    {
      title: 'Exploração livre',
      description: 'Tempo livre para descobrir locais interessantes por conta própria',
      start_time: '14:00',
      location_name: 'Centro da cidade',
      address: 'Centro da cidade, Brasil',
      latitude: 0,
      longitude: 0,
      activity_type: 'attraction',
      cost_estimate: 0
    },
    {
      title: isLastDay ? 'Check-out e despedida' : 'Jantar regional',
      description: isLastDay ?
        'Check-out do hotel e preparação para partida' :
        'Experimente a gastronomia local em um restaurante típico',
      start_time: isLastDay ? '17:00' : '19:00',
      location_name: isLastDay ? 'Hotel/Hospedagem' : 'Restaurante Local',
      address: 'Centro da cidade, Brasil',
      latitude: 0,
      longitude: 0,
      activity_type: isLastDay ? 'hotel' : 'restaurant',
      cost_estimate: isLastDay ? 0 : 60
    }
  ];
}

function determineActivityType(title: string, description: string): string {
  const text = (title + ' ' + description).toLowerCase();
  
  if (text.match(/restaurante|café|comida|almoço|jantar|comer|bar|lanchonete/)) return 'restaurant';
  if (text.match(/museu|galeria|arte|cultura|memorial|centro cultural/)) return 'museum';
  if (text.match(/parque|praia|natureza|trilha|jardim|reserva/)) return 'park';
  if (text.match(/shopping|compras|mercado|feira|loja/)) return 'shopping';
  if (text.match(/hotel|hospedagem|pousada|resort/)) return 'hotel';
  if (text.match(/transporte|táxi|ônibus|metrô|trem|aeroporto/)) return 'transportation';
  if (text.match(/show|teatro|cinema|entretenimento|espetáculo|festival/)) return 'entertainment';
  
  return 'attraction';
}

// Enhanced coordinate matching with multiple strategies + Google Places fallback
async function findOptimalCoordinatesWithFallback(
  locationName: string, 
  coordinatesMap: Map<string, Coordinates>
): Promise<Coordinates | null> {
  
  // Strategy 1: Direct match from Gemini coordinates (highest priority)
  const directKey = `${locationName.toLowerCase()}|direct`;
  const directMatch = coordinatesMap.get(directKey);
  if (directMatch) {
    console.log(`🎯 Direct Gemini match: ${locationName}`);
    return directMatch;
  }
  
  // Strategy 2: Exact name match from function calls
  for (const [key, coords] of coordinatesMap.entries()) {
    if (key.includes(locationName.toLowerCase()) && !key.includes('|direct')) {
      console.log(`🔍 Function call match: ${locationName}`);
      return coords;
    }
  }
  
  // Strategy 3: Fuzzy name matching
  const normalizedLocation = normalizeLocationName(locationName);
  for (const [key, coords] of coordinatesMap.entries()) {
    const normalizedKey = normalizeLocationName(key.split('|')[0] || '');
    if (calculateSimilarity(normalizedLocation, normalizedKey) > 0.7) {
      console.log(`🔄 Fuzzy match: ${locationName} -> ${key}`);
      return coords;
    }
  }
  
  // Strategy 4: Partial word matching
  const locationWords = locationName.toLowerCase().split(/\s+/);
  for (const [key, coords] of coordinatesMap.entries()) {
    const keyWords = key.toLowerCase().split(/[\s|]+/);
    const matchingWords = locationWords.filter(word => 
      word.length > 3 && keyWords.some(keyWord => keyWord.includes(word) || word.includes(keyWord))
    );
    
    if (matchingWords.length >= Math.min(2, locationWords.length)) {
      console.log(`🔤 Word match: ${locationName} -> ${key}`);
      return coords;
    }
  }
  
  // Strategy 5: Search in local GLOBAL_LOCATIONS_DB (local fallback)
  const fallbackCoordinates = await searchInLocalDatabase(locationName);
  if (fallbackCoordinates) {
    console.log(`🗄️ Local database match: ${locationName}`);
    return fallbackCoordinates;
  }
  
  // Strategy 6: NEW - Google Places API fallback (última opção)
  console.log(`🚨 Google Places fallback acionado para: ${locationName}`);
  try {
    const { getOptimizedCoordinates } = await import('./coordinates-cache');
    
    // Tentar determinar o tipo da atividade automaticamente
    const activityType = determineActivityTypeFromName(locationName);
    
    // Assumir São Paulo como cidade padrão (poderia ser melhorado)
    const googleResult = await getOptimizedCoordinates(
      locationName, 
      activityType, 
      'São Paulo', 
      true
    );
    
    if (googleResult) {
      console.log(`✅ Google Places fallback SUCCESS: ${locationName} [${googleResult.latitude}, ${googleResult.longitude}]`);
      return googleResult;
    }
  } catch (error) {
    console.warn(`❌ Google Places fallback FAILED for ${locationName}:`, error);
  }
  
  return null;
}

function normalizeLocationName(name: string): string {
  return name
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove accents
    .replace(/[^\w\s]/g, '') // Remove special chars
    .replace(/\s+/g, ' ')
    .trim();
}

function calculateSimilarity(str1: string, str2: string): number {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;
  
  if (longer.length === 0) return 1.0;
  
  const editDistance = levenshteinDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
}

function levenshteinDistance(str1: string, str2: string): number {
  const matrix: number[][] = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(0));
  
  for (let i = 0; i <= str1.length; i++) matrix[0]![i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j]![0] = j;
  
  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j]![i] = Math.min(
        matrix[j]![i - 1]! + 1,     // deletion
        matrix[j - 1]![i]! + 1,     // insertion
        matrix[j - 1]![i - 1]! + indicator // substitution
      );
    }
  }
  
  return matrix[str2.length]![str1.length]!;
}

function extractSection(lines: string[], section: string): string | undefined {
  const sectionIndex = lines.findIndex(line => line.includes(section));
  if (sectionIndex >= 0 && lines[sectionIndex + 1]) {
    return lines[sectionIndex + 1];
  }
  return undefined;
}

function extractCostInfo(lines: string[]): { min: number; max: number } {
  const costLine = lines.find(line => line.match(/Min:\s*R\$\s*([0-9.,]+).*?Max:\s*R\$\s*([0-9.,]+)/i));
  if (costLine) {
    const costMatch = costLine.match(/Min:\s*R\$\s*([0-9.,]+).*?Max:\s*R\$\s*([0-9.,]+)/i);
    if (costMatch && costMatch[1] && costMatch[2]) {
      const minCost = parseFloat(costMatch[1].replace(/[.,]/g, '')) || 800;
      const maxCost = parseFloat(costMatch[2].replace(/[.,]/g, '')) || 2000;
      return { min: minCost, max: maxCost };
    }
  }
  return { min: 800, max: 2000 };
}

function extractChecklist(lines: string[]): Array<{ item_text: string; category: string }> {
  const checklist: Array<{ item_text: string; category: string }> = [];
  
  const checklistIndex = lines.findIndex(line => line.includes('**CHECKLIST'));
  
  if (checklistIndex >= 0) {
    const checklistLines = lines.slice(checklistIndex + 1, checklistIndex + 10);
    
    for (const line of checklistLines) {
      if (line.includes(':')) {
        const [categoryName, ...itemParts] = line.split(':');
        if (categoryName && itemParts.length > 0) {
          const items = itemParts.join(':').split(/[,;]/)
            .map(item => item.trim())
            .filter(item => item.length > 0 && item.length < 100);
          
          let category = 'general';
          const catName = categoryName.toLowerCase();
          if (catName.includes('documento')) category = 'documents';
          else if (catName.includes('bagagem')) category = 'packing';
          else if (catName.includes('reserva')) category = 'bookings';
          
          items.forEach(item => {
            checklist.push({ item_text: item, category });
          });
        }
      }
    }
  }

  if (checklist.length === 0) {
    [
      { item_text: 'RG ou passaporte', category: 'documents' },
      { item_text: 'Comprovante de vacinação', category: 'documents' },
      { item_text: 'Roupas adequadas ao clima', category: 'packing' },
      { item_text: 'Protetor solar', category: 'packing' },
      { item_text: 'Câmera fotográfica', category: 'packing' },
      { item_text: 'Carregador portátil', category: 'packing' },
      { item_text: 'Reserva de hospedagem', category: 'bookings' },
      { item_text: 'Passagens de transporte', category: 'bookings' },
      { item_text: 'Seguro viagem', category: 'general' },
      { item_text: 'Dinheiro e cartões', category: 'general' }
    ].forEach(item => checklist.push(item));
  }

  return checklist;
}

export function generateMockItinerary(request: AIItineraryRequest): AIGeneratedItinerary {
  const firstCity = request.cities[0] ?? 'São Paulo';
  const mockTitle = `Roteiro ${request.trip_type} por ${request.cities.join(', ')}`;
  const coverImageUrl = '/nophoto.jpg'; // ✅ SIMPLES: Usar arquivo estático
  
  console.log(`[generateMockItinerary] Creating mock itinerary for:`, firstCity);
  console.log(`[generateMockItinerary] Cover image URL:`, coverImageUrl);
  
  return {
    title: mockTitle,
    description: `Um roteiro incrível de ${request.total_days} dias explorando as melhores experiências de ${request.cities.join(' e ')}.`,
    cover_image_url: coverImageUrl,
    estimated_cost_min: 800,
    estimated_cost_max: 2000,
    days: Array.from({ length: request.total_days }, (_, index) => ({
      day_number: index + 1,
      title: `Dia ${index + 1} - Explorando ${request.cities[index % request.cities.length] ?? firstCity}`,
      description: `Um dia repleto de descobertas em ${request.cities[index % request.cities.length] ?? firstCity}`,
      activities: generateDefaultActivities(request.cities[index % request.cities.length] ?? firstCity)
    })),
    checklist: [
      { item_text: 'RG ou passaporte', category: 'documents' },
      { item_text: 'Roupas adequadas', category: 'packing' },
      { item_text: 'Reserva de hospedagem', category: 'bookings' },
      { item_text: 'Seguro viagem', category: 'general' }
    ]
  };
}

export function generateDefaultActivities(city: string) {
  const cityCoords = getMockCoordinatesForCity(city);
  
  console.log(`[generateDefaultActivities] Creating activities for:`, city);
  
  const activities = [
    {
      title: `Café da manhã regional em ${city}`,
      description: 'Experimente especialidades locais em um café tradicional',
      start_time: '08:00',
      end_time: '09:30',
      location_name: `Café Central ${city}`,
      address: `Centro, ${city}`,
      latitude: cityCoords.lat + 0.001,
      longitude: cityCoords.lng + 0.001,
      activity_type: 'restaurant',
      cost_estimate: 25,
      photo_url: '/nophoto.jpg', // ✅ SIMPLES: Usar arquivo estático
      photo_attribution: 'Default placeholder'
    },
    {
      title: `Principal atração de ${city}`,
      description: 'Visite o ponto turístico mais importante da cidade',
      start_time: '10:00',
      end_time: '12:00',
      location_name: `Centro Histórico ${city}`,
      address: `Centro Histórico, ${city}`,
      latitude: cityCoords.lat + 0.002,
      longitude: cityCoords.lng + 0.002,
      activity_type: 'attraction',
      cost_estimate: 50,
      photo_url: '/nophoto.jpg', // ✅ SIMPLES: Usar arquivo estático
      photo_attribution: 'Default placeholder'
    },
    {
      title: `Almoço típico em ${city}`,
      description: 'Deguste a gastronomia regional',
      start_time: '12:30',
      end_time: '14:00',
      location_name: `Restaurante Típico ${city}`,
      address: `Centro, ${city}`,
      latitude: cityCoords.lat + 0.003,
      longitude: cityCoords.lng + 0.003,
      activity_type: 'restaurant',
      cost_estimate: 45,
      photo_url: '/nophoto.jpg', // ✅ SIMPLES: Usar arquivo estático
      photo_attribution: 'Default placeholder'
    }
  ];
  
  console.log(`[generateDefaultActivities] Generated ${activities.length} activities with static images`);
  
  return activities;
}

// Nova função para buscar no database local
async function searchInLocalDatabase(locationName: string): Promise<Coordinates | null> {
  const normalizedLocation = normalizeLocationName(locationName);
  
  // Busca direta no database com diferentes variações
  const searchVariations = [
    `${normalizedLocation}|restaurant|são paulo`,
    `${normalizedLocation}|attraction|são paulo`,
    `${normalizedLocation}|museum|são paulo`,
    `${normalizedLocation}|bar|são paulo`,
    `${normalizedLocation}|street|são paulo`,
    `${normalizedLocation}|park|são paulo`,
    `${normalizedLocation}|landmark|são paulo`,
    `${normalizedLocation}|airport|são paulo`,
    `${normalizedLocation}|theater|são paulo`,
    `${normalizedLocation}|market|são paulo`
  ];
  
  for (const variation of searchVariations) {
    const result = GLOBAL_LOCATIONS_DB.get(variation);
    if (result) {
      return result;
    }
  }
  
  // Busca fuzzy no database local
  for (const [key, coords] of GLOBAL_LOCATIONS_DB.entries()) {
    const keyLocation = key.split('|')[0] || '';
    const normalizedKey = normalizeLocationName(keyLocation);
    
    // Verificar similaridade entre nomes
    if (calculateSimilarity(normalizedLocation, normalizedKey) > 0.6) {
      return coords;
    }
    
    // Verificar se alguma palavra do location está no key
    const locationWords = normalizedLocation.split(/\s+/);
    const keyWords = normalizedKey.split(/\s+/);
    
    const matchingWords = locationWords.filter(word => 
      word.length > 3 && keyWords.some(keyWord => 
        keyWord.includes(word) || word.includes(keyWord)
      )
    );
    
    if (matchingWords.length >= Math.ceil(locationWords.length * 0.5)) {
      return coords;
    }
  }
  
  return null;
}

// Função auxiliar para determinar tipo de atividade pelo nome
function determineActivityTypeFromName(locationName: string): string {
  const name = locationName.toLowerCase();
  
  if (name.includes('restaurante') || name.includes('bar') || name.includes('café')) return 'restaurant';
  if (name.includes('museu') || name.includes('galeria')) return 'museum';
  if (name.includes('parque') || name.includes('jardim')) return 'park';
  if (name.includes('shopping') || name.includes('mercado')) return 'shopping';
  if (name.includes('hotel') || name.includes('pousada')) return 'hotel';
  if (name.includes('aeroporto') || name.includes('estação')) return 'transportation';
  if (name.includes('teatro') || name.includes('cinema')) return 'entertainment';
  if (name.includes('igreja') || name.includes('catedral')) return 'landmark';
  if (name.includes('rua') || name.includes('avenida')) return 'street';
  
  return 'attraction'; // padrão
} 