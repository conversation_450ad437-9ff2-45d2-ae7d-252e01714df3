"use client";

import React, { useState } from 'react';
import { 
  Clock, 
  MapPin, 
  CheckCircle, 
  Play, 
  Pause, 
  SkipFor<PERSON>,
  Eye,
  Navigation,
  Calendar
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TimelineActivity {
  id: string;
  title: string;
  description?: string;
  location_name?: string;
  start_time?: string;
  end_time?: string;
  day_number: number;
  status?: 'pending' | 'in_progress' | 'completed' | 'skipped';
  started_at?: string;
  completed_at?: string;
  notes?: string;
}

interface TravelModeTimelineProps {
  activities: TimelineActivity[];
  currentDay: number;
  onActivitySelect?: (activityId: string) => void;
  onActivityAction?: (activityId: string, action: 'start' | 'complete' | 'skip') => void;
  className?: string;
}

function formatTimeFromString(timeString: string): string {
  try {
    const [hours, minutes] = timeString.split(':');
    return `${hours}:${minutes}`;
  } catch {
    return timeString;
  }
}

function getStatusIcon(status?: string) {
  switch (status) {
    case 'completed':
      return <CheckCircle className="w-4 h-4 text-green-600" />;
    case 'in_progress':
      return <Play className="w-4 h-4 text-blue-600" />;
    case 'skipped':
      return <SkipForward className="w-4 h-4 text-orange-600" />;
    default:
      return <Clock className="w-4 h-4 text-zinc-400" />;
  }
}

function getStatusColor(status?: string) {
  switch (status) {
    case 'completed':
      return 'border-green-500 bg-green-50 dark:bg-green-950/30';
    case 'in_progress':
      return 'border-blue-500 bg-blue-50 dark:bg-blue-950/30';
    case 'skipped':
      return 'border-orange-500 bg-orange-50 dark:bg-orange-950/30';
    default:
      return 'border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-900';
  }
}

function TimelineItem({ 
  activity, 
  isLast, 
  onActivitySelect, 
  onActivityAction 
}: { 
  activity: TimelineActivity; 
  isLast: boolean;
  onActivitySelect?: (activityId: string) => void;
  onActivityAction?: (activityId: string, action: 'start' | 'complete' | 'skip') => void;
}) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="relative flex gap-4">
      {/* Timeline Line */}
      <div className="flex flex-col items-center">
        <div className={cn(
          "w-3 h-3 rounded-full border-2 flex-shrink-0",
          getStatusColor(activity.status).includes('border-green') ? 'bg-green-500 border-green-500' :
          getStatusColor(activity.status).includes('border-blue') ? 'bg-blue-500 border-blue-500' :
          getStatusColor(activity.status).includes('border-orange') ? 'bg-orange-500 border-orange-500' :
          'bg-zinc-300 border-zinc-300 dark:bg-zinc-600 dark:border-zinc-600'
        )} />
        {!isLast && (
          <div className="w-0.5 h-16 bg-zinc-200 dark:bg-zinc-700 mt-2" />
        )}
      </div>

      {/* Content */}
      <div className="flex-1 pb-8">
        <div 
          className={cn(
            "rounded-lg border p-4 cursor-pointer transition-all duration-200",
            getStatusColor(activity.status),
            "hover:shadow-md"
          )}
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                {getStatusIcon(activity.status)}
                {activity.start_time && (
                  <span className="text-sm font-medium text-zinc-600 dark:text-zinc-300">
                    {formatTimeFromString(activity.start_time)}
                  </span>
                )}
              </div>
              <h3 className="font-semibold text-zinc-900 dark:text-white mb-1">
                {activity.title}
              </h3>
              {activity.location_name && (
                <div className="flex items-center gap-1 text-sm text-zinc-500 dark:text-zinc-400">
                  <MapPin className="w-3 h-3" />
                  {activity.location_name}
                </div>
              )}
            </div>
          </div>

          {/* Expanded Content */}
          {isExpanded && (
            <div className="mt-4 pt-4 border-t border-zinc-200 dark:border-zinc-700">
              {activity.description && (
                <p className="text-sm text-zinc-600 dark:text-zinc-300 mb-3">
                  {activity.description}
                </p>
              )}

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-2">
                {activity.status === 'pending' && onActivityAction && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onActivityAction(activity.id, 'start');
                    }}
                    className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                  >
                    <Play className="w-3 h-3" />
                    Iniciar
                  </button>
                )}

                {activity.status === 'in_progress' && onActivityAction && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onActivityAction(activity.id, 'complete');
                    }}
                    className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                  >
                    <CheckCircle className="w-3 h-3" />
                    Concluir
                  </button>
                )}

                {(activity.status === 'pending' || activity.status === 'in_progress') && onActivityAction && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onActivityAction(activity.id, 'skip');
                    }}
                    className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-orange-600 text-white rounded hover:bg-orange-700 transition-colors"
                  >
                    <SkipForward className="w-3 h-3" />
                    Pular
                  </button>
                )}

                {onActivitySelect && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onActivitySelect(activity.id);
                    }}
                    className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-zinc-600 text-white rounded hover:bg-zinc-700 transition-colors"
                  >
                    <Eye className="w-3 h-3" />
                    Ver no Mapa
                  </button>
                )}
              </div>

              {/* Status Info */}
              {activity.status === 'completed' && activity.completed_at && (
                <div className="mt-2 text-xs text-green-600 dark:text-green-400">
                  ✅ Concluída em {new Date(activity.completed_at).toLocaleString('pt-BR')}
                </div>
              )}

              {activity.status === 'in_progress' && activity.started_at && (
                <div className="mt-2 text-xs text-blue-600 dark:text-blue-400">
                  🚀 Iniciada em {new Date(activity.started_at).toLocaleString('pt-BR')}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function TravelModeTimeline({
  activities,
  currentDay,
  onActivitySelect,
  onActivityAction,
  className
}: TravelModeTimelineProps) {
  // Filtrar atividades do dia atual e ordenar por horário
  const dayActivities = activities
    .filter(activity => activity.day_number === currentDay)
    .sort((a, b) => {
      if (!a.start_time || !b.start_time) return 0;
      return a.start_time.localeCompare(b.start_time);
    });

  if (dayActivities.length === 0) {
    return (
      <div className={cn(
        "flex items-center justify-center h-64 text-center",
        className
      )}>
        <div className="text-zinc-500 dark:text-zinc-400">
          <Calendar className="w-8 h-8 mx-auto mb-2" />
          <p>Nenhuma atividade encontrada para o dia {currentDay}</p>
        </div>
      </div>
    );
  }

  // Calcular altura mínima baseada no número de atividades
  const minHeight = Math.max(600, dayActivities.length * 120 + 200); // Mesma lógica do TimelineMap

  return (
    <div className={cn("space-y-0 h-full", className)}>
      {/* Header */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-zinc-900 dark:text-white mb-2">
          Timeline do Dia {currentDay}
        </h2>
        <p className="text-sm text-zinc-500 dark:text-zinc-400">
          {dayActivities.length} atividade{dayActivities.length !== 1 ? 's' : ''} programada{dayActivities.length !== 1 ? 's' : ''}
        </p>
      </div>

      {/* Timeline */}
      <div className="space-y-0">
        {dayActivities.map((activity, index) => (
          <TimelineItem
            key={activity.id}
            activity={activity}
            isLast={index === dayActivities.length - 1}
            onActivitySelect={onActivitySelect}
            onActivityAction={onActivityAction}
          />
        ))}
      </div>
    </div>
  );
}
