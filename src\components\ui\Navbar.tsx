'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { Button } from '@/components/ui/button';
import { Menu, X, Search, Bell, MessageCircle, Compass, User, MapPin } from 'lucide-react';
import AuthButton from '@/components/auth/AuthButton';
import { NotificationBadge } from '@/components/social/notifications/NotificationBadge';

// Types
interface User {
  id: string;
  email?: string;
  user_metadata?: Record<string, any>;
  [key: string]: any;
}

interface NavItem {
  name: string;
  href: string;
  icon: React.ReactNode;
  requiresAuth?: boolean;
}

// Navigation items
const navigationItems: NavItem[] = [
  {
    name: 'Feed',
    href: '/feed',
    icon: <Compass className="h-5 w-5" />,
    requiresAuth: true,
  },
  {
    name: 'Roteiro<PERSON>',
    href: '/itineraries',
    icon: <MapPin className="h-5 w-5" />,
    requiresAuth: false,
  },
  {
    name: 'Men<PERSON><PERSON>',
    href: '/messages',
    icon: <MessageCircle className="h-5 w-5" />,
    requiresAuth: true,
  },

  {
    name: 'Meu Perfil',
    href: '/profile',
    icon: <User className="h-5 w-5" />,
    requiresAuth: true,
  },
];

const publicItems: NavItem[] = [
  {
    name: 'Início',
    href: '/',
    icon: <Compass className="h-5 w-5" />,
  },
  {
    name: 'Roteiros',
    href: '/itineraries',
    icon: <MapPin className="h-5 w-5" />,
  },
  {
    name: 'Sobre',
    href: '/about',
    icon: <User className="h-5 w-5" />,
  },
];

export default function Navbar() {
  const [user, setUser] = useState<User | null>(null);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const pathname = usePathname();
  const router = useRouter();

  // Fetch user data
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const supabase = createClient();
        const { data: { user } } = await supabase.auth.getUser();
        setUser(user);
      } catch (error) {
        console.error('Error fetching user:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
  }, [pathname]);

  // Check if current path is active
  const isActiveLink = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  // Toggle mobile menu
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  // Get appropriate navigation items
  const navItems = user ? navigationItems : publicItems;

  if (loading) {
    return (
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200">
        <div className="container mx-auto px-4 h-16 flex items-center justify-between">
          <div className="w-32 h-8 bg-gray-200 animate-pulse rounded" />
          <div className="flex items-center space-x-4">
            <div className="w-8 h-8 bg-gray-200 animate-pulse rounded" />
            <div className="w-8 h-8 bg-gray-200 animate-pulse rounded" />
          </div>
        </div>
      </header>
    );
  }

  return (
    <>
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200">
        <div className="container mx-auto px-4 h-16 flex items-center justify-between">
          {/* Logo */}
          <Link 
            href={user ? '/feed' : '/'} 
            className="flex items-center space-x-2 text-xl font-bold text-blue-600 hover:text-blue-700 transition-colors"
          >
            <span className="text-2xl">🌍</span>
            <span className="hidden sm:block">Voyagr</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-1">
            {navItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                prefetch={item.href === '/profile' ? false : true}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                  isActiveLink(item.href)
                    ? 'bg-blue-100 text-blue-700 font-medium'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                }`}
              >
                {item.icon}
                <span className="hidden lg:block">{item.name}</span>
              </Link>
            ))}
          </nav>

          {/* Right Section */}
          <div className="flex items-center space-x-2">
            {/* Search Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/search')}
              className="p-2"
              aria-label="Buscar"
            >
              <Search className="h-5 w-5" />
            </Button>

            {/* Notification Badge - Only for authenticated users */}
            {user && (
              <NotificationBadge className="hidden md:block" />
            )}

            {/* Auth Section */}
            <div className="hidden md:block">
              <AuthButton user={user} />
            </div>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleMenu}
              className="md:hidden p-2"
              aria-label="Menu"
            >
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-gray-200 bg-white">
            <nav className="container mx-auto px-4 py-4 space-y-2">
              {/* Search link for mobile */}
              <Link
                href="/search"
                className="flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 text-gray-600 hover:bg-gray-100 hover:text-gray-900"
              >
                <Search className="h-5 w-5" />
                <span>Buscar</span>
              </Link>

              {/* Notification Badge for mobile - Only for authenticated users */}
              {user && (
                <div className="px-4 py-3">
                  <NotificationBadge showLabel={true} className="w-full justify-start" />
                </div>
              )}
              
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  prefetch={item.href === '/profile' ? false : true}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                    isActiveLink(item.href)
                      ? 'bg-blue-100 text-blue-700 font-medium'
                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}
                >
                  {item.icon}
                  <span>{item.name}</span>
                </Link>
              ))}
              
              {/* Mobile Auth Section */}
              <div className="pt-4 border-t border-gray-200">
                <AuthButton user={user} />
              </div>
            </nav>
          </div>
        )}
      </header>

      {/* Spacer to prevent content from hiding behind fixed navbar */}
      <div className="h-16" />
    </>
  );
} 