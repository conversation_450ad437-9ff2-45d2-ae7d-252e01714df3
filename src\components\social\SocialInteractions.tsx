'use client';

import React, { useState, useOptimistic, useTransition } from 'react';
import { Heart, MessageCircle, Share, Bookmark, MoreHorizontal, Edit, Trash2 } from 'lucide-react';
import { toggleLikeAction, toggleSavePostAction, deletePostAction } from '@/app/feed/social-actions';
import { ReactionPicker, ReactionSummary } from './ReactionPicker';
import { addPostReaction, removePostReaction } from '@/lib/actions/reactions';
import type { ReactionType, PostReactionSummary } from '@/types/social';

interface SocialInteractionsProps {
  postId: string;
  initialLikesCount: number;
  initialCommentsCount: number;
  initialSharesCount: number;
  isInitiallyLiked: boolean;
  isInitiallySaved?: boolean;
  isOwnPost?: boolean;
  onCommentClick?: () => void;
  onEditClick?: () => void;
  onLike?: ((postId: string) => void) | undefined;
  // Novas props para reações
  userReaction?: ReactionType;
  reactions?: PostReactionSummary;
  enableAdvancedReactions?: boolean;
}

type OptimisticState = {
  isLiked: boolean;
  likesCount: number;
  isSaved: boolean;
  userReaction: ReactionType | undefined;
  totalReactions: number;
};

type OptimisticAction = {
  type: 'like' | 'save' | 'reaction';
  value?: any;
};

export function SocialInteractions({
  postId,
  initialLikesCount,
  initialCommentsCount,
  initialSharesCount,
  isInitiallyLiked,
  isInitiallySaved = false,
  isOwnPost = false,
  onCommentClick,
  onEditClick,
  onLike,
  userReaction,
  reactions,
  enableAdvancedReactions = true,
}: SocialInteractionsProps) {
  const [isPending, startTransition] = useTransition();
  const [showOptions, setShowOptions] = useState(false);

  // Calcular total de reações
  const totalReactions = reactions ? Object.values(reactions).reduce((sum, r) => sum + r.count, 0) : initialLikesCount;

  // Optimistic state using React 19 useOptimistic
  const [optimisticState, addOptimistic] = useOptimistic<OptimisticState, OptimisticAction>(
    {
      isLiked: isInitiallyLiked,
      likesCount: initialLikesCount,
      isSaved: isInitiallySaved,
      userReaction,
      totalReactions,
    },
    (state: OptimisticState, action: OptimisticAction) => {
      switch (action.type) {
        case 'like':
          return {
            ...state,
            isLiked: !state.isLiked,
            likesCount: state.isLiked ? state.likesCount - 1 : state.likesCount + 1
          };
        case 'save':
          return {
            ...state,
            isSaved: !state.isSaved
          };
        case 'reaction':
          const newReaction = action.value as ReactionType | undefined;
          const hadReaction = !!state.userReaction;
          const willHaveReaction = !!newReaction;

          return {
            ...state,
            userReaction: newReaction,
            totalReactions: hadReaction && !willHaveReaction
              ? state.totalReactions - 1
              : !hadReaction && willHaveReaction
                ? state.totalReactions + 1
                : state.totalReactions,
          };
        default:
          return state;
      }
    }
  );

  const handleLike = () => {
    startTransition(async () => {
      // Optimistic update inside transition
      addOptimistic({ type: 'like' });
      
      const formData = new FormData();
      formData.append('postId', postId);
      
      try {
        await toggleLikeAction(formData);
        onLike?.(postId);
      } catch (error) {
        console.error('Error liking post:', error);
        // Revert optimistic update on error
        addOptimistic({ type: 'like' });
      }
    });
  };

  const handleSave = () => {
    startTransition(async () => {
      // Optimistic update inside transition
      addOptimistic({ type: 'save' });
      
      const formData = new FormData();
      formData.append('postId', postId);
      
      try {
        await toggleSavePostAction(formData);
      } catch (error) {
        console.error('Error saving post:', error);
        // Revert optimistic update on error
        addOptimistic({ type: 'save' });
      }
    });
  };

  const handleDelete = () => {
    if (!confirm('Tem certeza que deseja excluir este post? Esta ação não pode ser desfeita.')) {
      return;
    }

    startTransition(async () => {
      const formData = new FormData();
      formData.append('postId', postId);

      try {
        await deletePostAction(formData);
      } catch (error) {
        console.error('Error deleting post:', error);
      }
    });
  };

  // Handlers para reações avançadas
  const handleReactionSelect = (reaction: ReactionType) => {
    startTransition(async () => {
      addOptimistic({ type: 'reaction', value: reaction });

      try {
        await addPostReaction(postId, reaction);
      } catch (error) {
        console.error('Error adding reaction:', error);
        // Reverter otimismo em caso de erro
        addOptimistic({ type: 'reaction', value: optimisticState.userReaction });
      }
    });
  };

  const handleReactionRemove = () => {
    startTransition(async () => {
      addOptimistic({ type: 'reaction', value: undefined });

      try {
        await removePostReaction(postId);
      } catch (error) {
        console.error('Error removing reaction:', error);
        // Reverter otimismo em caso de erro
        addOptimistic({ type: 'reaction', value: optimisticState.userReaction });
      }
    });
  };

  return (
    <div className='flex items-center justify-between px-4 py-3 border-t border-gray-100'>
      <div className='flex items-center space-x-6'>
        {/* Reaction Picker ou Like button tradicional */}
        {enableAdvancedReactions ? (
          <div className="flex items-center space-x-2">
            <ReactionPicker
              currentReaction={optimisticState.userReaction}
              onReactionSelect={handleReactionSelect}
              onRemoveReaction={handleReactionRemove}
              disabled={isPending}
            />
            {reactions && (
              <ReactionSummary
                reactions={reactions}
                totalCount={optimisticState.totalReactions}
                className="ml-2"
              />
            )}
          </div>
        ) : (
          <button
            onClick={handleLike}
            disabled={isPending}
            className={`flex items-center space-x-2 px-3 py-2 rounded-full transition-all duration-200 ${
              optimisticState.isLiked
                ? 'text-red-600 bg-red-50 hover:bg-red-100'
                : 'text-gray-600 hover:text-red-600 hover:bg-red-50'
            } ${isPending ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <Heart
              className={`w-5 h-5 transition-all duration-200 ${
                optimisticState.isLiked ? 'fill-current scale-110' : ''
              }`}
            />
            <span className='text-sm font-medium'>{optimisticState.likesCount}</span>
          </button>
        )}

        {/* Comment button */}
        <button
          onClick={onCommentClick}
          className='flex items-center space-x-2 px-3 py-2 rounded-full text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200'
        >
          <MessageCircle className='w-5 h-5' />
          <span className='text-sm font-medium'>{initialCommentsCount}</span>
        </button>

        {/* Share button */}
        <button className='flex items-center space-x-2 px-3 py-2 rounded-full text-gray-600 hover:text-green-600 hover:bg-green-50 transition-all duration-200'>
          <Share className='w-5 h-5' />
          <span className='text-sm font-medium'>{initialSharesCount}</span>
        </button>
      </div>

      {/* Right side actions */}
      <div className='flex items-center space-x-2'>
        {/* Bookmark button */}
        <button 
          onClick={handleSave}
          disabled={isPending}
          className={`p-2 rounded-full transition-all duration-200 ${
            optimisticState.isSaved
              ? 'text-yellow-600 bg-yellow-50 hover:bg-yellow-100'
              : 'text-gray-600 hover:text-yellow-600 hover:bg-yellow-50'
          } ${isPending ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <Bookmark className={`w-5 h-5 ${optimisticState.isSaved ? 'fill-current' : ''}`} />
        </button>

        {/* Options menu for own posts */}
        {isOwnPost && (
          <div className='relative'>
            <button
              onClick={() => setShowOptions(!showOptions)}
              className='p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-full transition-all duration-200'
            >
              <MoreHorizontal className='w-5 h-5' />
            </button>

            {showOptions && (
              <div className='absolute right-0 top-full mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[160px]'>
                <button
                  onClick={() => {
                    onEditClick?.();
                    setShowOptions(false);
                  }}
                  className='flex items-center space-x-2 w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-50 first:rounded-t-lg transition-colors'
                >
                  <Edit className='w-4 h-4' />
                  <span>Editar</span>
                </button>
                <button
                  onClick={() => {
                    handleDelete();
                    setShowOptions(false);
                  }}
                  disabled={isPending}
                  className='flex items-center space-x-2 w-full px-4 py-2 text-left text-red-600 hover:bg-red-50 last:rounded-b-lg transition-colors disabled:opacity-50'
                >
                  <Trash2 className='w-4 h-4' />
                  <span>Excluir</span>
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Close options menu when clicking outside */}
      {showOptions && (
        <div 
          className='fixed inset-0 z-0' 
          onClick={() => setShowOptions(false)}
        />
      )}
    </div>
  );
}
